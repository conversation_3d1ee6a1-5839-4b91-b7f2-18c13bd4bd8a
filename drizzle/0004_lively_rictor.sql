CREATE TABLE IF NOT EXISTS "app-template_payment_intent" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"user_id" varchar(255) NOT NULL,
	"created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "app-template_payment_intent" ADD CONSTRAINT "app-template_payment_intent_user_id_app-template_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."app-template_user"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "payment_intent_user_id_idx" ON "app-template_payment_intent" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "payment_intent_created_at_idx" ON "app-template_payment_intent" USING btree ("created_at");