{"id": "1a9651f2-8b7f-4529-8083-6af79558e0fc", "prevId": "********-0000-0000-0000-************", "version": "7", "dialect": "postgresql", "tables": {"public.app-template_account": {"name": "app-template_account", "schema": "", "columns": {"user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "provider_account_id": {"name": "provider_account_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": false}, "token_type": {"name": "token_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false}, "session_state": {"name": "session_state", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}}, "indexes": {"account_user_id_idx": {"name": "account_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"app-template_account_user_id_app-template_user_id_fk": {"name": "app-template_account_user_id_app-template_user_id_fk", "tableFrom": "app-template_account", "tableTo": "app-template_user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"app-template_account_provider_provider_account_id_pk": {"name": "app-template_account_provider_provider_account_id_pk", "columns": ["provider", "provider_account_id"]}}, "uniqueConstraints": {}}, "public.app-template_post": {"name": "app-template_post", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "app-template_post_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "**********", "cache": "1", "cycle": false}}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"created_by_idx": {"name": "created_by_idx", "columns": [{"expression": "created_by", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "name_idx": {"name": "name_idx", "columns": [{"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"app-template_post_created_by_app-template_user_id_fk": {"name": "app-template_post_created_by_app-template_user_id_fk", "tableFrom": "app-template_post", "tableTo": "app-template_user", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.app-template_session": {"name": "app-template_session", "schema": "", "columns": {"session_token": {"name": "session_token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "expires": {"name": "expires", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}}, "indexes": {"session_user_id_idx": {"name": "session_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"app-template_session_user_id_app-template_user_id_fk": {"name": "app-template_session_user_id_app-template_user_id_fk", "tableFrom": "app-template_session", "tableTo": "app-template_user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.app-template_user": {"name": "app-template_user", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "email_verified": {"name": "email_verified", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "CURRENT_TIMESTAMP"}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.app-template_verification_token": {"name": "app-template_verification_token", "schema": "", "columns": {"identifier": {"name": "identifier", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "expires": {"name": "expires", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"app-template_verification_token_identifier_token_pk": {"name": "app-template_verification_token_identifier_token_pk", "columns": ["identifier", "token"]}}, "uniqueConstraints": {}}}, "enums": {}, "schemas": {}, "sequences": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}