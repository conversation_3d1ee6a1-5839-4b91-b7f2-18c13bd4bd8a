{"id": "86b73420-95cb-484d-85c3-0ea77744d67f", "prevId": "1a9651f2-8b7f-4529-8083-6af79558e0fc", "version": "7", "dialect": "postgresql", "tables": {"public.app-template_account": {"name": "app-template_account", "schema": "", "columns": {"user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "provider_account_id": {"name": "provider_account_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": false}, "token_type": {"name": "token_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false}, "session_state": {"name": "session_state", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}}, "indexes": {"account_user_id_idx": {"name": "account_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"app-template_account_user_id_app-template_user_id_fk": {"name": "app-template_account_user_id_app-template_user_id_fk", "tableFrom": "app-template_account", "tableTo": "app-template_user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"app-template_account_provider_provider_account_id_pk": {"name": "app-template_account_provider_provider_account_id_pk", "columns": ["provider", "provider_account_id"]}}, "uniqueConstraints": {}}, "public.app-template_asset": {"name": "app-template_asset", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "user-id": {"name": "user-id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"app-template_asset_user-id_app-template_user_id_fk": {"name": "app-template_asset_user-id_app-template_user_id_fk", "tableFrom": "app-template_asset", "tableTo": "app-template_user", "columnsFrom": ["user-id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"app-template_asset_url_unique": {"name": "app-template_asset_url_unique", "nullsNotDistinct": false, "columns": ["url"]}}}, "public.app-template_creation": {"name": "app-template_creation", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "user-id": {"name": "user-id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "prompt": {"name": "prompt", "type": "text", "primaryKey": false, "notNull": false}, "ai_model": {"name": "ai_model", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "credits_consumed": {"name": "credits_consumed", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"app-template_creation_user-id_app-template_user_id_fk": {"name": "app-template_creation_user-id_app-template_user_id_fk", "tableFrom": "app-template_creation", "tableTo": "app-template_user", "columnsFrom": ["user-id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "app-template_creation_user-id_app-template_asset_id_fk": {"name": "app-template_creation_user-id_app-template_asset_id_fk", "tableFrom": "app-template_creation", "tableTo": "app-template_asset", "columnsFrom": ["user-id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.app-template_post": {"name": "app-template_post", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "app-template_post_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"created_by_idx": {"name": "created_by_idx", "columns": [{"expression": "created_by", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "name_idx": {"name": "name_idx", "columns": [{"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"app-template_post_created_by_app-template_user_id_fk": {"name": "app-template_post_created_by_app-template_user_id_fk", "tableFrom": "app-template_post", "tableTo": "app-template_user", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.app-template_session": {"name": "app-template_session", "schema": "", "columns": {"session_token": {"name": "session_token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "expires": {"name": "expires", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}}, "indexes": {"session_user_id_idx": {"name": "session_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"app-template_session_user_id_app-template_user_id_fk": {"name": "app-template_session_user_id_app-template_user_id_fk", "tableFrom": "app-template_session", "tableTo": "app-template_user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.app-template_subscription_plan": {"name": "app-template_subscription_plan", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "plan_name": {"name": "plan_name", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "price_monthly": {"name": "price_monthly", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "credits_per_month": {"name": "credits_per_month", "type": "integer", "primaryKey": false, "notNull": true}, "features": {"name": "features", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"app-template_subscription_plan_plan_name_unique": {"name": "app-template_subscription_plan_plan_name_unique", "nullsNotDistinct": false, "columns": ["plan_name"]}}}, "public.app-template_user": {"name": "app-template_user", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "email_verified": {"name": "email_verified", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "CURRENT_TIMESTAMP"}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.app-template_verification_token": {"name": "app-template_verification_token", "schema": "", "columns": {"identifier": {"name": "identifier", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "expires": {"name": "expires", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"app-template_verification_token_identifier_token_pk": {"name": "app-template_verification_token_identifier_token_pk", "columns": ["identifier", "token"]}}, "uniqueConstraints": {}}}, "enums": {}, "schemas": {}, "sequences": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}