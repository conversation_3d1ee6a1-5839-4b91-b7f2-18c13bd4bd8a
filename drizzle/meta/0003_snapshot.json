{"id": "9722be35-bed6-4df1-bd09-7735b6109411", "prevId": "9027f1d5-8283-49c6-b586-fcfc36febf7a", "version": "7", "dialect": "postgresql", "tables": {"public.app-template_account": {"name": "app-template_account", "schema": "", "columns": {"user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "provider_account_id": {"name": "provider_account_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": false}, "token_type": {"name": "token_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false}, "session_state": {"name": "session_state", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}}, "indexes": {"account_user_id_idx": {"name": "account_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"app-template_account_user_id_app-template_user_id_fk": {"name": "app-template_account_user_id_app-template_user_id_fk", "tableFrom": "app-template_account", "tableTo": "app-template_user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"app-template_account_provider_provider_account_id_pk": {"name": "app-template_account_provider_provider_account_id_pk", "columns": ["provider", "provider_account_id"]}}, "uniqueConstraints": {}}, "public.app-template_asset": {"name": "app-template_asset", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "user-id": {"name": "user-id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"app-template_asset_user-id_app-template_user_id_fk": {"name": "app-template_asset_user-id_app-template_user_id_fk", "tableFrom": "app-template_asset", "tableTo": "app-template_user", "columnsFrom": ["user-id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"app-template_asset_url_unique": {"name": "app-template_asset_url_unique", "nullsNotDistinct": false, "columns": ["url"]}}}, "public.app-template_creation": {"name": "app-template_creation", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "user-id": {"name": "user-id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "prompt": {"name": "prompt", "type": "text", "primaryKey": false, "notNull": false}, "ai_model": {"name": "ai_model", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "credits_consumed": {"name": "credits_consumed", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"app-template_creation_user-id_app-template_user_id_fk": {"name": "app-template_creation_user-id_app-template_user_id_fk", "tableFrom": "app-template_creation", "tableTo": "app-template_user", "columnsFrom": ["user-id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "app-template_creation_user-id_app-template_asset_id_fk": {"name": "app-template_creation_user-id_app-template_asset_id_fk", "tableFrom": "app-template_creation", "tableTo": "app-template_asset", "columnsFrom": ["user-id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.app-template_credit_transactions": {"name": "app-template_credit_transactions", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "integer", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "related_id": {"name": "related_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"credit_transactions_user_id_idx": {"name": "credit_transactions_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "credit_transactions_type_idx": {"name": "credit_transactions_type_idx", "columns": [{"expression": "type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "credit_transactions_created_at_idx": {"name": "credit_transactions_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"app-template_credit_transactions_user_id_app-template_user_id_fk": {"name": "app-template_credit_transactions_user_id_app-template_user_id_fk", "tableFrom": "app-template_credit_transactions", "tableTo": "app-template_user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.app-template_session": {"name": "app-template_session", "schema": "", "columns": {"session_token": {"name": "session_token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "expires": {"name": "expires", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}}, "indexes": {"session_user_id_idx": {"name": "session_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"app-template_session_user_id_app-template_user_id_fk": {"name": "app-template_session_user_id_app-template_user_id_fk", "tableFrom": "app-template_session", "tableTo": "app-template_user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.app-template_subscription_plan": {"name": "app-template_subscription_plan", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "plan_name": {"name": "plan_name", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "price_monthly": {"name": "price_monthly", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "credits_per_month": {"name": "credits_per_month", "type": "integer", "primaryKey": false, "notNull": true}, "features": {"name": "features", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"app-template_subscription_plan_plan_name_unique": {"name": "app-template_subscription_plan_plan_name_unique", "nullsNotDistinct": false, "columns": ["plan_name"]}}}, "public.app-template_user_credits": {"name": "app-template_user_credits", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "total_credits": {"name": "total_credits", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "used_credits": {"name": "used_credits", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "daily_credits": {"name": "daily_credits", "type": "integer", "primaryKey": false, "notNull": true, "default": 10}, "last_daily_refresh": {"name": "last_daily_refresh", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"user_credits_user_id_idx": {"name": "user_credits_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"app-template_user_credits_user_id_app-template_user_id_fk": {"name": "app-template_user_credits_user_id_app-template_user_id_fk", "tableFrom": "app-template_user_credits", "tableTo": "app-template_user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.app-template_user": {"name": "app-template_user", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "email_verified": {"name": "email_verified", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "CURRENT_TIMESTAMP"}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "user_type": {"name": "user_type", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'free'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.app-template_verification_token": {"name": "app-template_verification_token", "schema": "", "columns": {"identifier": {"name": "identifier", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "expires": {"name": "expires", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"app-template_verification_token_identifier_token_pk": {"name": "app-template_verification_token_identifier_token_pk", "columns": ["identifier", "token"]}}, "uniqueConstraints": {}}}, "enums": {}, "schemas": {}, "sequences": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}