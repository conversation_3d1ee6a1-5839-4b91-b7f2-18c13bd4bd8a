"use client";

import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { Button } from '@/components/ui/button';
import { 
  Send, 
  X, 
  Eraser, 
  Paintbrush, 
  Scissors, 
  Copy, 
  Move,
  RotateCw,
  Loader2,
  <PERSON>rkles,
  Trash2,
  Eye,
  EyeOff
} from 'lucide-react';

interface SelectionArea {
  x: number;
  y: number;
  width: number;
  height: number;
}

interface RegionEditPanelProps {
  selection: SelectionArea;
  onEdit: (prompt: string, area: SelectionArea) => void;
  onClose: () => void;
  isProcessing?: boolean;
  className?: string;
}

const REGION_EDIT_PROMPTS = [
  {
    id: 'remove',
    label: '移除对象',
    prompt: '移除选中区域的对象，用背景填充',
    icon: Eraser,
    color: 'from-red-500 to-pink-500'
  },
  {
    id: 'enhance',
    label: '增强细节',
    prompt: '增强选中区域的细节和清晰度',
    icon: Sparkles,
    color: 'from-blue-500 to-purple-500'
  },
  {
    id: 'recolor',
    label: '重新着色',
    prompt: '改变选中区域的颜色',
    icon: Paintbrush,
    color: 'from-green-500 to-teal-500'
  },
  {
    id: 'replace',
    label: '替换内容',
    prompt: '用新内容替换选中区域',
    icon: Copy,
    color: 'from-orange-500 to-yellow-500'
  },
  {
    id: 'blur',
    label: '模糊处理',
    prompt: '对选中区域进行模糊处理',
    icon: Eye,
    color: 'from-gray-500 to-gray-600'
  },
  {
    id: 'sharpen',
    label: '锐化处理',
    prompt: '对选中区域进行锐化处理',
    icon: EyeOff,
    color: 'from-indigo-500 to-purple-600'
  }
];

export function RegionEditPanel({ 
  selection, 
  onEdit, 
  onClose, 
  isProcessing = false,
  className = "" 
}: RegionEditPanelProps) {
  const [customPrompt, setCustomPrompt] = useState('');
  const [showCustomInput, setShowCustomInput] = useState(false);

  const handleQuickEdit = useCallback((prompt: string) => {
    onEdit(prompt, selection);
  }, [onEdit, selection]);

  const handleCustomEdit = useCallback((e: React.FormEvent) => {
    e.preventDefault();
    if (customPrompt.trim()) {
      onEdit(customPrompt.trim(), selection);
      setCustomPrompt('');
      setShowCustomInput(false);
    }
  }, [customPrompt, onEdit, selection]);

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        className={`fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4 ${className}`}
        onClick={onClose}
      >
        <motion.div
          initial={{ y: 20 }}
          animate={{ y: 0 }}
          exit={{ y: 20 }}
          className="bg-white rounded-2xl shadow-2xl max-w-md w-full max-h-[80vh] overflow-hidden"
          onClick={(e) => e.stopPropagation()}
        >
          {/* 头部 */}
          <div className="p-6 border-b border-gray-100">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-800">区域编辑</h3>
                <p className="text-sm text-gray-500 mt-1">
                  选中区域: {Math.round(selection.width)} × {Math.round(selection.height)} px
                </p>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="h-8 w-8 p-0 rounded-full"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* 内容区域 */}
          <div className="p-6 space-y-4 max-h-[60vh] overflow-y-auto">
            {/* 快捷编辑选项 */}
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-3">快捷编辑</h4>
              <div className="grid grid-cols-2 gap-3">
                {REGION_EDIT_PROMPTS.map((prompt) => {
                  const Icon = prompt.icon;
                  return (
                    <motion.button
                      key={prompt.id}
                      onClick={() => handleQuickEdit(prompt.prompt)}
                      disabled={isProcessing}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      className="group relative p-4 rounded-xl border border-gray-200 bg-white hover:bg-gray-50 transition-all duration-200 text-left disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <div className="flex items-center gap-3 mb-2">
                        <div className={`p-2 rounded-lg bg-gradient-to-r ${prompt.color}`}>
                          <Icon className="h-4 w-4 text-white" />
                        </div>
                        <span className="text-sm font-medium text-gray-800">
                          {prompt.label}
                        </span>
                      </div>
                      <p className="text-xs text-gray-500 line-clamp-2">
                        {prompt.prompt}
                      </p>
                      
                      {/* 悬停效果 */}
                      <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                    </motion.button>
                  );
                })}
              </div>
            </div>

            {/* 自定义编辑 */}
            <div>
              <div className="flex items-center justify-between mb-3">
                <h4 className="text-sm font-medium text-gray-700">自定义编辑</h4>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowCustomInput(!showCustomInput)}
                  className="text-xs"
                >
                  {showCustomInput ? '收起' : '展开'}
                </Button>
              </div>
              
              <AnimatePresence>
                {showCustomInput && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    className="overflow-hidden"
                  >
                    <form onSubmit={handleCustomEdit} className="space-y-3">
                      <textarea
                        value={customPrompt}
                        onChange={(e) => setCustomPrompt(e.target.value)}
                        placeholder="描述你想对选中区域进行的具体编辑..."
                        disabled={isProcessing}
                        className="w-full h-20 p-3 rounded-xl border border-gray-200 bg-gray-50 text-sm placeholder-gray-500 resize-none focus:border-blue-400 focus:ring-2 focus:ring-blue-100 focus:outline-none disabled:opacity-50"
                      />
                      <Button
                        type="submit"
                        disabled={!customPrompt.trim() || isProcessing}
                        className="w-full bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600"
                      >
                        {isProcessing ? (
                          <>
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            处理中...
                          </>
                        ) : (
                          <>
                            <Send className="h-4 w-4 mr-2" />
                            应用编辑
                          </>
                        )}
                      </Button>
                    </form>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* 处理状态 */}
            {isProcessing && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="bg-blue-50 border border-blue-200 rounded-xl p-4"
              >
                <div className="flex items-center gap-3">
                  <div className="flex-shrink-0">
                    <div className="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center">
                      <Loader2 className="h-4 w-4 text-white animate-spin" />
                    </div>
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-blue-800">正在处理选中区域</p>
                    <p className="text-xs text-blue-600 mt-1">AI正在根据你的指令编辑选中区域...</p>
                  </div>
                </div>
              </motion.div>
            )}
          </div>

          {/* 底部操作 */}
          <div className="p-6 border-t border-gray-100 bg-gray-50">
            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={onClose}
                className="flex-1"
                disabled={isProcessing}
              >
                取消
              </Button>
              <Button
                onClick={() => setShowCustomInput(true)}
                className="flex-1 bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800"
                disabled={isProcessing}
              >
                <Paintbrush className="h-4 w-4 mr-2" />
                自定义编辑
              </Button>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}
