"use client";

import React, { useRef, useEffect, useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { Button } from '@/components/ui/button';
import { 
  MousePointer, 
  Square, 
  Move, 
  ZoomIn, 
  ZoomOut, 
  RotateCcw,
  Trash2,
  Check,
  X
} from 'lucide-react';

interface SelectionArea {
  x: number;
  y: number;
  width: number;
  height: number;
}

interface InteractiveCanvasProps {
  imageUrl: string;
  onAreaSelect?: (area: SelectionArea) => void;
  onGlobalEdit?: () => void;
  className?: string;
}

type Tool = 'select' | 'move' | 'zoom';

export function InteractiveCanvas({ 
  imageUrl, 
  onAreaSelect, 
  onGlobalEdit,
  className = "" 
}: InteractiveCanvasProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const imageRef = useRef<HTMLImageElement>(null);
  
  const [activeTool, setActiveTool] = useState<Tool>('select');
  const [isDrawing, setIsDrawing] = useState(false);
  const [selection, setSelection] = useState<SelectionArea | null>(null);
  const [startPoint, setStartPoint] = useState<{ x: number; y: number } | null>(null);
  const [scale, setScale] = useState(1);
  const [offset, setOffset] = useState({ x: 0, y: 0 });
  const [imageLoaded, setImageLoaded] = useState(false);
  const [canvasSize, setCanvasSize] = useState({ width: 0, height: 0 });

  // 初始化canvas和图片
  useEffect(() => {
    const canvas = canvasRef.current;
    const container = containerRef.current;
    if (!canvas || !container) return;

    const image = new Image();
    image.crossOrigin = 'anonymous';
    image.onload = () => {
      imageRef.current = image;
      
      // 计算canvas尺寸以适应容器
      const containerRect = container.getBoundingClientRect();
      const maxWidth = containerRect.width - 40; // 留出边距
      const maxHeight = Math.min(600, containerRect.height - 100);
      
      const aspectRatio = image.width / image.height;
      let canvasWidth = maxWidth;
      let canvasHeight = maxWidth / aspectRatio;
      
      if (canvasHeight > maxHeight) {
        canvasHeight = maxHeight;
        canvasWidth = maxHeight * aspectRatio;
      }
      
      canvas.width = canvasWidth;
      canvas.height = canvasHeight;
      
      setCanvasSize({ width: canvasWidth, height: canvasHeight });
      setImageLoaded(true);
      
      // 初始绘制
      drawCanvas();
    };
    
    image.src = imageUrl;
  }, [imageUrl]);

  // 绘制canvas内容
  const drawCanvas = useCallback(() => {
    const canvas = canvasRef.current;
    const image = imageRef.current;
    if (!canvas || !image || !imageLoaded) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // 清空canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // 绘制图片
    ctx.save();
    ctx.translate(offset.x, offset.y);
    ctx.scale(scale, scale);
    ctx.drawImage(image, 0, 0, canvas.width, canvas.height);
    ctx.restore();
    
    // 绘制选择区域
    if (selection) {
      ctx.strokeStyle = '#3b82f6';
      ctx.lineWidth = 2;
      ctx.setLineDash([5, 5]);
      ctx.strokeRect(selection.x, selection.y, selection.width, selection.height);
      
      // 绘制选择区域的遮罩
      ctx.fillStyle = 'rgba(59, 130, 246, 0.1)';
      ctx.fillRect(selection.x, selection.y, selection.width, selection.height);
      
      // 绘制角点
      const cornerSize = 8;
      ctx.fillStyle = '#3b82f6';
      ctx.setLineDash([]);
      
      // 四个角点
      const corners = [
        { x: selection.x - cornerSize/2, y: selection.y - cornerSize/2 },
        { x: selection.x + selection.width - cornerSize/2, y: selection.y - cornerSize/2 },
        { x: selection.x - cornerSize/2, y: selection.y + selection.height - cornerSize/2 },
        { x: selection.x + selection.width - cornerSize/2, y: selection.y + selection.height - cornerSize/2 }
      ];
      
      corners.forEach(corner => {
        ctx.fillRect(corner.x, corner.y, cornerSize, cornerSize);
      });
    }
  }, [imageLoaded, scale, offset, selection]);

  // 重绘canvas
  useEffect(() => {
    drawCanvas();
  }, [drawCanvas]);

  // 获取鼠标在canvas上的坐标
  const getCanvasCoordinates = useCallback((event: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return { x: 0, y: 0 };
    
    const rect = canvas.getBoundingClientRect();
    return {
      x: event.clientX - rect.left,
      y: event.clientY - rect.top
    };
  }, []);

  // 处理鼠标按下
  const handleMouseDown = useCallback((event: React.MouseEvent<HTMLCanvasElement>) => {
    if (activeTool !== 'select') return;
    
    const coords = getCanvasCoordinates(event);
    setIsDrawing(true);
    setStartPoint(coords);
    setSelection(null);
  }, [activeTool, getCanvasCoordinates]);

  // 处理鼠标移动
  const handleMouseMove = useCallback((event: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing || !startPoint || activeTool !== 'select') return;
    
    const coords = getCanvasCoordinates(event);
    const newSelection: SelectionArea = {
      x: Math.min(startPoint.x, coords.x),
      y: Math.min(startPoint.y, coords.y),
      width: Math.abs(coords.x - startPoint.x),
      height: Math.abs(coords.y - startPoint.y)
    };
    
    setSelection(newSelection);
  }, [isDrawing, startPoint, activeTool, getCanvasCoordinates]);

  // 处理鼠标抬起
  const handleMouseUp = useCallback(() => {
    if (isDrawing && selection && onAreaSelect) {
      onAreaSelect(selection);
    }
    setIsDrawing(false);
    setStartPoint(null);
  }, [isDrawing, selection, onAreaSelect]);

  // 清除选择
  const clearSelection = useCallback(() => {
    setSelection(null);
  }, []);

  // 缩放控制
  const handleZoomIn = useCallback(() => {
    setScale(prev => Math.min(prev * 1.2, 3));
  }, []);

  const handleZoomOut = useCallback(() => {
    setScale(prev => Math.max(prev / 1.2, 0.5));
  }, []);

  // 重置视图
  const resetView = useCallback(() => {
    setScale(1);
    setOffset({ x: 0, y: 0 });
    setSelection(null);
  }, []);

  return (
    <div ref={containerRef} className={`relative bg-gray-50 rounded-xl overflow-hidden ${className}`}>
      {/* 工具栏 */}
      <div className="absolute top-4 left-4 z-10 flex gap-2 bg-white/90 backdrop-blur-sm rounded-lg p-2 shadow-lg">
        <Button
          size="sm"
          variant={activeTool === 'select' ? 'default' : 'outline'}
          onClick={() => setActiveTool('select')}
          className="h-8 w-8 p-0"
        >
          <Square className="h-4 w-4" />
        </Button>
        <Button
          size="sm"
          variant={activeTool === 'move' ? 'default' : 'outline'}
          onClick={() => setActiveTool('move')}
          className="h-8 w-8 p-0"
        >
          <Move className="h-4 w-4" />
        </Button>
        <div className="w-px bg-gray-300 mx-1" />
        <Button
          size="sm"
          variant="outline"
          onClick={handleZoomIn}
          className="h-8 w-8 p-0"
        >
          <ZoomIn className="h-4 w-4" />
        </Button>
        <Button
          size="sm"
          variant="outline"
          onClick={handleZoomOut}
          className="h-8 w-8 p-0"
        >
          <ZoomOut className="h-4 w-4" />
        </Button>
        <Button
          size="sm"
          variant="outline"
          onClick={resetView}
          className="h-8 w-8 p-0"
        >
          <RotateCcw className="h-4 w-4" />
        </Button>
      </div>

      {/* 选择区域操作按钮 */}
      <AnimatePresence>
        {selection && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            className="absolute top-4 right-4 z-10 flex gap-2 bg-white/90 backdrop-blur-sm rounded-lg p-2 shadow-lg"
          >
            <Button
              size="sm"
              onClick={() => onAreaSelect?.(selection)}
              className="h-8 bg-blue-500 hover:bg-blue-600 text-white"
            >
              <Check className="h-4 w-4 mr-1" />
              编辑选区
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={clearSelection}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Canvas */}
      <div className="flex items-center justify-center p-4 min-h-[400px]">
        {imageLoaded ? (
          <canvas
            ref={canvasRef}
            className="border border-gray-200 rounded-lg shadow-sm cursor-crosshair"
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onMouseLeave={handleMouseUp}
          />
        ) : (
          <div className="flex items-center justify-center w-full h-64 bg-gray-100 rounded-lg">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
              <p className="text-gray-500">加载图片中...</p>
            </div>
          </div>
        )}
      </div>

      {/* 全局编辑按钮 */}
      <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-10">
        <Button
          onClick={onGlobalEdit}
          className="bg-gradient-to-r from-rose-500 to-orange-500 hover:from-rose-600 hover:to-orange-600 text-white shadow-lg"
        >
          <MousePointer className="h-4 w-4 mr-2" />
          全局编辑
        </Button>
      </div>
    </div>
  );
}
