"use client";

import { useState, useRef, useCallback } from "react";
import { motion, AnimatePresence } from "motion/react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { 
  Clock, 
  Download, 
  Copy, 
  Trash2, 
  Eye, 
  EyeOff,
  RotateCcw,
  Maximize2,
  X,
  ChevronLeft,
  ChevronRight
} from "lucide-react";

interface HistoryItem {
  id: number;
  originalImage: string;
  resultImage: string;
  prompt: string;
  timestamp: string;
  settings?: {
    quality: number;
    preserveAspect: boolean;
    outputFormat: 'jpg' | 'png' | 'webp';
    enhanceDetails: boolean;
  };
}

interface EnhancedHistorySectionProps {
  history: HistoryItem[];
  setHistory: (history: HistoryItem[]) => void;
  downloadImage: (imageUrl: string, filename: string) => void;
  setUploadedImage: (image: string | null) => void;
  setPrompt: (prompt: string) => void;
  setResult: (result: string | null) => void;
  isMainContent?: boolean; // When no image is uploaded
}

interface BeforeAfterComparisonProps {
  beforeImage: string;
  afterImage: string;
  prompt: string;
  onClose: () => void;
}

// Before/After Comparison Modal
function BeforeAfterComparison({ beforeImage, afterImage, prompt, onClose }: BeforeAfterComparisonProps) {
  const [sliderPosition, setSliderPosition] = useState(50);
  const [isDragging, setIsDragging] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (!isDragging || !containerRef.current) return;
    
    const rect = containerRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));
    setSliderPosition(percentage);
  }, [isDragging]);

  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    if (!isDragging || !containerRef.current) return;
    
    const rect = containerRef.current.getBoundingClientRect();
    const x = e.touches[0].clientX - rect.left;
    const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));
    setSliderPosition(percentage);
  }, [isDragging]);

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      onClick={onClose}
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        className="bg-background rounded-xl max-w-4xl w-full max-h-[90vh] overflow-hidden"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="p-4 border-b flex items-center justify-between">
          <div>
            <h3 className="font-semibold">Before & After Comparison</h3>
            <p className="text-sm text-muted-foreground line-clamp-1">{prompt}</p>
          </div>
          <Button variant="ghost" size="icon" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Comparison Container */}
        <div className="p-4">
          <div
            ref={containerRef}
            className="relative w-full aspect-video rounded-lg overflow-hidden cursor-col-resize select-none"
            onMouseMove={handleMouseMove}
            onMouseDown={() => setIsDragging(true)}
            onMouseUp={() => setIsDragging(false)}
            onMouseLeave={() => setIsDragging(false)}
            onTouchMove={handleTouchMove}
            onTouchStart={() => setIsDragging(true)}
            onTouchEnd={() => setIsDragging(false)}
          >
            {/* After Image (Background) */}
            <img
              src={afterImage}
              alt="After"
              className="absolute inset-0 w-full h-full object-cover"
              draggable={false}
            />
            
            {/* Before Image (Clipped) */}
            <div
              className="absolute inset-0 overflow-hidden"
              style={{ clipPath: `inset(0 ${100 - sliderPosition}% 0 0)` }}
            >
              <img
                src={beforeImage}
                alt="Before"
                className="w-full h-full object-cover"
                draggable={false}
              />
            </div>

            {/* Slider Line */}
            <div
              className="absolute top-0 bottom-0 w-0.5 bg-white shadow-lg z-10"
              style={{ left: `${sliderPosition}%` }}
            >
              {/* Slider Handle */}
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-8 h-8 bg-white rounded-full shadow-lg flex items-center justify-center">
                <div className="flex gap-0.5">
                  <ChevronLeft className="h-3 w-3" />
                  <ChevronRight className="h-3 w-3" />
                </div>
              </div>
            </div>

            {/* Labels */}
            <div className="absolute top-4 left-4 bg-black/50 text-white px-2 py-1 rounded text-sm">
              Before
            </div>
            <div className="absolute top-4 right-4 bg-black/50 text-white px-2 py-1 rounded text-sm">
              After
            </div>
          </div>

          {/* Instructions */}
          <p className="text-center text-sm text-muted-foreground mt-4">
            Drag the slider or click to compare before and after
          </p>
        </div>
      </motion.div>
    </motion.div>
  );
}

// Individual History Item Component
interface HistoryItemProps {
  item: HistoryItem;
  index: number;
  isMainContent: boolean;
  onDownload: (imageUrl: string, filename: string) => void;
  onReuse: (item: HistoryItem) => void;
  onDelete: (id: number) => void;
  onCompare: (item: HistoryItem) => void;
}

function HistoryItemCard({ item, index, isMainContent, onDownload, onReuse, onDelete, onCompare }: HistoryItemProps) {
  const [showOriginal, setShowOriginal] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, scale: 0.9 }}
      transition={{ delay: index * 0.1 }}
      className={`group relative rounded-xl overflow-hidden bg-card/50 backdrop-blur-sm border hover:shadow-xl transition-all duration-300 ${
        isMainContent ? 'hover:scale-[1.02]' : 'hover:scale-105'
      }`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => {
        setIsHovered(false);
        setShowOriginal(false);
      }}
    >
      {/* Image Container */}
      <div className={`relative ${isMainContent ? 'aspect-[4/3]' : 'aspect-video'} overflow-hidden`}>
        {/* Main Image */}
        <img
          src={showOriginal ? item.originalImage : item.resultImage}
          alt={`Generated image: ${item.prompt}`}
          className="w-full h-full object-cover transition-all duration-300"
        />

        {/* Hover Overlay */}
        <div className={`absolute inset-0 bg-black/60 transition-opacity duration-200 ${
          isHovered ? 'opacity-100' : 'opacity-0'
        }`}>
          {/* Action Buttons */}
          <div className="absolute inset-0 flex items-center justify-center gap-2">
            <Button
              variant="outline"
              size="icon"
              className="h-10 w-10 bg-white/10 backdrop-blur-sm border-white/20 hover:bg-white/20"
              onClick={() => onCompare(item)}
            >
              <Eye className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="icon"
              className="h-10 w-10 bg-white/10 backdrop-blur-sm border-white/20 hover:bg-white/20"
              onClick={() => onDownload(item.resultImage, `history-${item.id}`)}
            >
              <Download className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="icon"
              className="h-10 w-10 bg-white/10 backdrop-blur-sm border-white/20 hover:bg-white/20"
              onClick={() => onReuse(item)}
            >
              <Copy className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="icon"
              className="h-10 w-10 bg-white/10 backdrop-blur-sm border-white/20 hover:bg-white/20"
              onClick={() => onDelete(item.id)}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>

          {/* Before/After Toggle */}
          <div className="absolute bottom-4 left-4 right-4">
            <Button
              variant="outline"
              size="sm"
              className="w-full bg-white/10 backdrop-blur-sm border-white/20 hover:bg-white/20"
              onMouseDown={() => setShowOriginal(true)}
              onMouseUp={() => setShowOriginal(false)}
              onMouseLeave={() => setShowOriginal(false)}
              onTouchStart={() => setShowOriginal(true)}
              onTouchEnd={() => setShowOriginal(false)}
            >
              {showOriginal ? (
                <>
                  <EyeOff className="h-4 w-4 mr-2" />
                  Showing Original
                </>
              ) : (
                <>
                  <Eye className="h-4 w-4 mr-2" />
                  Hold to see Original
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Quality Badge */}
        {item.settings && (
          <div className="absolute top-3 right-3 bg-black/50 text-white px-2 py-1 rounded text-xs font-medium">
            {item.settings.quality}%
          </div>
        )}

        {/* Format Badge */}
        {item.settings && (
          <div className="absolute top-3 left-3 bg-black/50 text-white px-2 py-1 rounded text-xs font-medium uppercase">
            {item.settings.outputFormat}
          </div>
        )}
      </div>

      {/* Content */}
      <div className="p-4 space-y-3">
        <p className={`font-medium leading-tight ${
          isMainContent ? 'text-base line-clamp-2' : 'text-sm line-clamp-1'
        }`}>
          {item.prompt}
        </p>
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <span>{item.timestamp}</span>
          <div className="flex items-center gap-2">
            <Clock className="h-3 w-3" />
            <span>Generated</span>
          </div>
        </div>
      </div>
    </motion.div>
  );
}

export function EnhancedHistorySection({
  history,
  setHistory,
  downloadImage,
  setUploadedImage,
  setPrompt,
  setResult,
  isMainContent = false
}: EnhancedHistorySectionProps) {
  const [selectedComparison, setSelectedComparison] = useState<HistoryItem | null>(null);

  const handleReuse = useCallback((item: HistoryItem) => {
    setUploadedImage(item.originalImage);
    setPrompt(item.prompt);
    setResult(null);
  }, [setUploadedImage, setPrompt, setResult]);

  const handleDelete = useCallback((id: number) => {
    setHistory(history.filter(item => item.id !== id));
  }, [history, setHistory]);

  const handleCompare = useCallback((item: HistoryItem) => {
    setSelectedComparison(item);
  }, []);

  if (history.length === 0) {
    return null;
  }

  return (
    <>
      <Card className={`bg-card/50 backdrop-blur-sm border-none ${
        isMainContent ? 'shadow-xl' : ''
      }`}>
        <CardContent className={isMainContent ? "p-8" : "p-6"}>
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <Clock className={`${isMainContent ? 'h-6 w-6' : 'h-4 w-4'} text-primary`} />
              <div>
                <h3 className={`font-semibold ${isMainContent ? 'text-2xl' : 'text-lg'}`}>
                  {isMainContent ? 'Your Creation Gallery' : 'Generation History'}
                </h3>
                <p className="text-sm text-muted-foreground">
                  {history.length} {history.length === 1 ? 'creation' : 'creations'}
                  {isMainContent && ' • Click any image to reuse or compare'}
                </p>
              </div>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setHistory([])}
              className="text-xs"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Clear All
            </Button>
          </div>

          {/* Masonry Grid */}
          <div className={`grid gap-4 ${
            isMainContent 
              ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5' 
              : 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
          }`}>
            <AnimatePresence>
              {history.map((item, index) => (
                <HistoryItemCard
                  key={item.id}
                  item={item}
                  index={index}
                  isMainContent={isMainContent}
                  onDownload={downloadImage}
                  onReuse={handleReuse}
                  onDelete={handleDelete}
                  onCompare={handleCompare}
                />
              ))}
            </AnimatePresence>
          </div>
        </CardContent>
      </Card>

      {/* Before/After Comparison Modal */}
      <AnimatePresence>
        {selectedComparison && (
          <BeforeAfterComparison
            beforeImage={selectedComparison.originalImage}
            afterImage={selectedComparison.resultImage}
            prompt={selectedComparison.prompt}
            onClose={() => setSelectedComparison(null)}
          />
        )}
      </AnimatePresence>
    </>
  );
}
