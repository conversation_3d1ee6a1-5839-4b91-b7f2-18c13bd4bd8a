"use client";

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { Button } from '@/components/ui/button';
import { 
  Sparkles, 
  Scissors, 
  Palette, 
  Sun, 
  Moon, 
  Contrast,
  Zap,
  Wand2,
  Eye,
  Crop,
  RotateCw,
  Maximize,
  Minimize,
  Filter,
  Camera,
  Image as ImageIcon,
  Layers,
  ChevronDown,
  ChevronUp
} from 'lucide-react';

interface QuickAction {
  id: string;
  label: string;
  prompt: string;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
  category: 'basic' | 'color' | 'effect' | 'transform';
  description: string;
}

interface QuickActionsPanelProps {
  onActionSelect: (prompt: string) => void;
  isProcessing?: boolean;
  className?: string;
}

const QUICK_ACTIONS: QuickAction[] = [
  // 基础编辑
  {
    id: 'remove-bg',
    label: '去除背景',
    prompt: '移除图片背景，保留主体对象',
    icon: Scissors,
    color: 'from-red-500 to-pink-500',
    category: 'basic',
    description: '自动识别并移除图片背景'
  },
  {
    id: 'enhance-detail',
    label: '增强细节',
    prompt: '增强图片细节，提高清晰度和锐度',
    icon: Sparkles,
    color: 'from-blue-500 to-purple-500',
    category: 'basic',
    description: '提升图片整体清晰度'
  },
  {
    id: 'upscale',
    label: '放大图片',
    prompt: '放大图片分辨率，保持清晰度',
    icon: Maximize,
    color: 'from-green-500 to-teal-500',
    category: 'basic',
    description: '智能放大图片尺寸'
  },
  {
    id: 'denoise',
    label: '降噪处理',
    prompt: '去除图片噪点，让图片更干净',
    icon: Filter,
    color: 'from-gray-500 to-gray-600',
    category: 'basic',
    description: '减少图片中的噪点'
  },

  // 色彩调整
  {
    id: 'brighten',
    label: '调亮图片',
    prompt: '调亮图片，增加亮度',
    icon: Sun,
    color: 'from-yellow-500 to-orange-500',
    category: 'color',
    description: '提升图片整体亮度'
  },
  {
    id: 'darken',
    label: '调暗图片',
    prompt: '调暗图片，降低亮度',
    icon: Moon,
    color: 'from-indigo-500 to-purple-500',
    category: 'color',
    description: '降低图片整体亮度'
  },
  {
    id: 'enhance-color',
    label: '色彩增强',
    prompt: '增强色彩饱和度，让颜色更鲜艳',
    icon: Palette,
    color: 'from-pink-500 to-red-500',
    category: 'color',
    description: '让颜色更加鲜艳'
  },
  {
    id: 'adjust-contrast',
    label: '调整对比度',
    prompt: '调整图片对比度，让明暗更分明',
    icon: Contrast,
    color: 'from-gray-600 to-gray-800',
    category: 'color',
    description: '增强明暗对比效果'
  },

  // 特效处理
  {
    id: 'artistic',
    label: '艺术化',
    prompt: '将图片转换为艺术风格',
    icon: Wand2,
    color: 'from-purple-500 to-indigo-500',
    category: 'effect',
    description: '添加艺术风格效果'
  },
  {
    id: 'vintage',
    label: '复古风格',
    prompt: '添加复古滤镜效果',
    icon: Camera,
    color: 'from-amber-500 to-orange-600',
    category: 'effect',
    description: '营造怀旧复古氛围'
  },
  {
    id: 'black-white',
    label: '黑白效果',
    prompt: '转换为黑白照片',
    icon: ImageIcon,
    color: 'from-gray-400 to-gray-700',
    category: 'effect',
    description: '转换为经典黑白风格'
  },
  {
    id: 'professional',
    label: '专业修图',
    prompt: '专业级图片修饰，优化整体效果',
    icon: Zap,
    color: 'from-emerald-500 to-teal-500',
    category: 'effect',
    description: '一键专业级修图'
  }
];

const CATEGORY_LABELS = {
  basic: '基础编辑',
  color: '色彩调整',
  effect: '特效处理',
  transform: '变形处理'
};

export function QuickActionsPanel({ 
  onActionSelect, 
  isProcessing = false,
  className = "" 
}: QuickActionsPanelProps) {
  const [activeCategory, setActiveCategory] = useState<string>('basic');
  const [isExpanded, setIsExpanded] = useState(false);

  const categories = Array.from(new Set(QUICK_ACTIONS.map(action => action.category)));
  const filteredActions = QUICK_ACTIONS.filter(action => action.category === activeCategory);

  return (
    <div className={`bg-white/90 backdrop-blur-sm rounded-2xl border border-gray-200 shadow-lg ${className}`}>
      {/* 头部 */}
      <div className="p-4 border-b border-gray-100">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-800">快捷指令</h3>
            <p className="text-sm text-gray-500">一键应用常用编辑效果</p>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
            className="h-8 w-8 p-0"
          >
            {isExpanded ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
        </div>
      </div>

      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="overflow-hidden"
          >
            {/* 分类标签 */}
            <div className="p-4 pb-2">
              <div className="flex gap-2 overflow-x-auto">
                {categories.map((category) => (
                  <Button
                    key={category}
                    variant={activeCategory === category ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setActiveCategory(category)}
                    className="whitespace-nowrap text-xs"
                  >
                    {CATEGORY_LABELS[category as keyof typeof CATEGORY_LABELS]}
                  </Button>
                ))}
              </div>
            </div>

            {/* 快捷操作网格 */}
            <div className="p-4 pt-2">
              <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-3">
                {filteredActions.map((action) => {
                  const Icon = action.icon;
                  return (
                    <motion.button
                      key={action.id}
                      onClick={() => onActionSelect(action.prompt)}
                      disabled={isProcessing}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      className="group relative p-4 rounded-xl border border-gray-200 bg-white hover:bg-gray-50 transition-all duration-200 text-left disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <div className="flex flex-col items-center text-center space-y-2">
                        <div className={`p-3 rounded-xl bg-gradient-to-r ${action.color}`}>
                          <Icon className="h-5 w-5 text-white" />
                        </div>
                        <div>
                          <h4 className="text-sm font-medium text-gray-800">
                            {action.label}
                          </h4>
                          <p className="text-xs text-gray-500 mt-1 line-clamp-2">
                            {action.description}
                          </p>
                        </div>
                      </div>
                      
                      {/* 悬停效果 */}
                      <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                    </motion.button>
                  );
                })}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 收起状态下的快速访问 */}
      {!isExpanded && (
        <div className="p-4">
          <div className="flex gap-2 overflow-x-auto">
            {QUICK_ACTIONS.slice(0, 4).map((action) => {
              const Icon = action.icon;
              return (
                <motion.button
                  key={action.id}
                  onClick={() => onActionSelect(action.prompt)}
                  disabled={isProcessing}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="flex-shrink-0 p-3 rounded-xl border border-gray-200 bg-white hover:bg-gray-50 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <div className={`p-2 rounded-lg bg-gradient-to-r ${action.color}`}>
                    <Icon className="h-4 w-4 text-white" />
                  </div>
                </motion.button>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
}
