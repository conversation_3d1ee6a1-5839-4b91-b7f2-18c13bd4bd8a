"use client";

import React, { useState, useCallback, useRef, useEffect } from "react";
import { motion, AnimatePresence } from "motion/react";
import { Send, Upload, RotateCcw } from "lucide-react";
import { api } from "@/trpc/react";
import { ImageComparison } from "@/components/ui/image-comparison";

interface SimpleImageEditorProps {
  onEdit: (prompt: string, imageUrl: string) => Promise<string>;
  onDownload?: (imageUrl: string) => void;
  isProcessing?: boolean;
  className?: string;
  initialImageUrl?: string | null;
  onImageUpload?: (imageUrl: string) => void;
}

export function SimpleImageEditor({
  onEdit,
  onDownload,
  isProcessing = false,
  className = "",
  initialImageUrl = null,
  onImageUpload,
}: SimpleImageEditorProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const uploadingRef = useRef(false);

  const [currentImage, setCurrentImage] = useState<string | null>(null);
  const [originalImage, setOriginalImage] = useState<string | null>(null);
  const [resultImage, setResultImage] = useState<string | null>(null);

  const [customPrompt, setCustomPrompt] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [isUploading, setIsUploading] = useState(false);

  // 上传API
  const uploadMutation = api.upload.upload.useMutation();

  // 处理初始图片URL
  useEffect(() => {
    if (initialImageUrl && initialImageUrl !== currentImage) {
      setCurrentImage(initialImageUrl);
      setOriginalImage(initialImageUrl);
      setResultImage(null);
    }
  }, [initialImageUrl, currentImage]);

  // 处理文件上传到R2
  const handleFileUpload = useCallback(
    async (file: File) => {
      if (!file.type.startsWith("image/")) {
        uploadingRef.current = false;
        return;
      }

      try {
        setIsUploading(true);
        uploadingRef.current = true;

        // 首先显示本地预览
        const reader = new FileReader();
        reader.onload = (e) => {
          const result = e.target?.result as string;
          setCurrentImage(result);
          setOriginalImage(result);
          setResultImage(null);
        };
        reader.readAsDataURL(file);

        // 获取上传签名URL
        const { signedUrl, key } = await uploadMutation.mutateAsync({
          fileType: file.type,
          fileSize: file.size,
        });

        // 上传文件到R2
        const uploadResponse = await fetch(signedUrl, {
          method: "PUT",
          body: file,
          headers: {
            "Content-Type": file.type,
          },
        });

        if (!uploadResponse.ok) {
          throw new Error("上传失败");
        }

        // 构建R2公共URL
        const r2PublicUrl = `${process.env.NEXT_PUBLIC_R2_PUBLIC_URL}/${key}`;

        // 更新为R2 URL
        setCurrentImage(r2PublicUrl);
        setOriginalImage(r2PublicUrl);

        // 通知父组件有新图片上传
        onImageUpload?.(r2PublicUrl);
      } catch (error) {
        console.error("文件上传失败:", error);
        // 如果上传失败，保持本地预览
      } finally {
        setIsUploading(false);
        uploadingRef.current = false;
      }
    },
    [uploadMutation, onImageUpload],
  );

  // 处理拖拽上传
  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      if (uploadingRef.current) return;

      const files = Array.from(e.dataTransfer.files);
      const imageFile = files.find((file) => file.type.startsWith("image/"));

      if (imageFile) {
        uploadingRef.current = true;
        void handleFileUpload(imageFile);
      }
    },
    [handleFileUpload],
  );

  // 处理自定义编辑
  const handleCustomEdit = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault();
      if (!customPrompt.trim() || !currentImage) return;

      try {
        setIsGenerating(true);
        setOriginalImage(currentImage);
        setResultImage(null);

        const newImageUrl = await onEdit(customPrompt.trim(), currentImage);
        setResultImage(newImageUrl);
        setCurrentImage(newImageUrl);
        setCustomPrompt("");
      } catch (error) {
        console.error("Custom edit failed:", error);
      } finally {
        setIsGenerating(false);
      }
    },
    [customPrompt, currentImage, onEdit],
  );

  // 重置到原始图片
  const handleReset = useCallback(() => {
    if (originalImage) {
      setCurrentImage(originalImage);
      setResultImage(null);
      setCustomPrompt("");
    }
  }, [originalImage]);

  return (
    <div className={`relative ${className}`}>
      {/* 隐藏的文件输入 */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={(e) => {
          const file = e.target.files?.[0];
          if (file && !uploadingRef.current) {
            uploadingRef.current = true;
            void handleFileUpload(file);
          }
        }}
        className="hidden"
      />

      {/* 主要内容区域 */}
      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-gray-50 to-gray-100 shadow-xl">
        {/* 主图片显示区域 */}
        <div className="flex h-full items-center justify-center px-8 py-8">
          <div className="relative w-full max-w-5xl">
            {currentImage ? (
              <div className="relative flex w-full justify-center">
                {/* 生成完成后显示对比组件 */}
                {resultImage && originalImage && !isGenerating ? (
                  <div className="w-full max-w-4xl">
                    <ImageComparison
                      beforeImage={originalImage}
                      afterImage={resultImage}
                      title="AI图片编辑对比"
                      className="overflow-hidden rounded-2xl shadow-2xl"
                    />

                    {/* 重新编辑按钮 */}
                    <div className="mt-6 flex justify-center">
                      <motion.button
                        onClick={handleReset}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        className="flex items-center gap-2 rounded-xl bg-gradient-to-r from-gray-500 to-gray-600 px-6 py-3 font-medium text-white shadow-lg transition-all duration-300 hover:from-gray-600 hover:to-gray-700"
                      >
                        <RotateCcw className="h-4 w-4" />
                        重新编辑
                      </motion.button>
                    </div>
                  </div>
                ) : (
                  /* 生成中或未生成时显示单张图片 */
                  <>
                    <div className="relative">
                      <img
                        src={currentImage}
                        alt="Current image"
                        className="max-h-[600px] max-w-[800px] rounded-lg object-contain shadow-2xl"
                        onDrop={handleDrop}
                        onDragOver={(e) => e.preventDefault()}
                      />

                      {/* 生成中的动画覆盖层 */}
                      {isGenerating && (
                        <motion.div
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          exit={{ opacity: 0 }}
                          className="absolute inset-0 flex items-center justify-center rounded-lg bg-black/20 backdrop-blur-sm"
                        >
                          {/* 粒子效果 - 铺满整个区域 */}
                          <div className="pointer-events-none absolute inset-0">
                            {Array.from({ length: 20 }, (_, i) => {
                              // 生成随机位置，确保粒子分布在整个区域
                              const randomX = Math.random() * 100;
                              const randomY = Math.random() * 100;
                              const colors = [
                                "bg-blue-400",
                                "bg-purple-400",
                                "bg-pink-400",
                                "bg-indigo-400",
                                "bg-cyan-400",
                              ];
                              const sizes = ["h-1 w-1", "h-2 w-2", "h-3 w-3"];

                              return (
                                <motion.div
                                  key={i}
                                  className={`absolute rounded-full ${colors[i % colors.length]} ${sizes[i % sizes.length]}`}
                                  style={{
                                    left: `${randomX}%`,
                                    top: `${randomY}%`,
                                  }}
                                  animate={{
                                    y: [-30, -80, -30],
                                    x: [0, Math.sin(i) * 20, 0],
                                    opacity: [0.2, 0.8, 0.2],
                                    scale: [0.5, 1.5, 0.5],
                                    rotate: [0, 360, 0],
                                  }}
                                  transition={{
                                    duration: 4 + Math.random() * 2, // 4-6秒随机持续时间
                                    repeat: Infinity,
                                    delay: i * 0.2, // 错开启动时间
                                    ease: "easeInOut",
                                  }}
                                />
                              );
                            })}

                            {/* 额外的流星效果 */}
                            {Array.from({ length: 8 }, (_, i) => (
                              <motion.div
                                key={`meteor-${i}`}
                                className="absolute h-1 w-8 bg-gradient-to-r from-transparent via-white to-transparent opacity-60"
                                style={{
                                  left: `${Math.random() * 100}%`,
                                  top: `${Math.random() * 100}%`,
                                  rotate: `${45 + Math.random() * 90}deg`,
                                }}
                                animate={{
                                  x: [-100, 100],
                                  opacity: [0, 0.8, 0],
                                }}
                                transition={{
                                  duration: 2,
                                  repeat: Infinity,
                                  delay: i * 0.8,
                                  ease: "easeInOut",
                                }}
                              />
                            ))}
                          </div>

                          <div className="relative z-10 rounded-xl bg-white/90 px-6 py-4 shadow-xl backdrop-blur-md">
                            <div className="flex items-center gap-3">
                              <motion.div
                                animate={{ rotate: 360 }}
                                transition={{
                                  duration: 2,
                                  repeat: Infinity,
                                  ease: "linear",
                                }}
                                className="h-6 w-6 rounded-full border-2 border-blue-500 border-t-transparent"
                              />
                              <span className="text-lg font-medium text-gray-800">
                                AI 生成中...
                              </span>
                            </div>
                          </div>
                        </motion.div>
                      )}
                    </div>

                    {/* 提示词输入框 - 悬浮在图片上方 */}
                    {!isGenerating && (
                      <motion.div
                        initial={{ opacity: 0, y: 30, x: "-50%" }}
                        animate={{ opacity: 1, y: 0, x: "-50%" }}
                        exit={{ opacity: 0, y: 30, x: "-50%" }}
                        transition={{ duration: 0.4, delay: 0.2 }}
                        className="absolute bottom-4 left-1/2 w-full max-w-2xl px-4"
                      >
                        <form onSubmit={handleCustomEdit} className="relative">
                          <div className="flex items-center gap-3 rounded-2xl border border-gray-200/50 bg-white/90 p-2 shadow-2xl backdrop-blur-md">
                            <input
                              type="text"
                              value={customPrompt}
                              onChange={(e) => setCustomPrompt(e.target.value)}
                              placeholder="输入自定义编辑指令..."
                              disabled={!currentImage || isGenerating}
                              className="flex-1 border-none bg-transparent px-4 py-2 text-base placeholder-gray-500 outline-none disabled:opacity-50"
                            />
                            <motion.button
                              type="submit"
                              disabled={
                                !customPrompt.trim() ||
                                !currentImage ||
                                isGenerating
                              }
                              whileHover={{
                                scale: 1.05,
                                boxShadow:
                                  "0 10px 25px rgba(59, 130, 246, 0.3)",
                              }}
                              whileTap={{ scale: 0.95 }}
                              className="group relative flex items-center gap-2 overflow-hidden rounded-xl bg-gradient-to-r from-blue-500 to-purple-600 px-6 py-2 font-medium text-white shadow-lg transition-all duration-300 hover:from-blue-600 hover:to-purple-700 disabled:cursor-not-allowed disabled:opacity-50 disabled:hover:scale-100"
                            >
                              {/* 按钮背景光效 */}
                              <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100" />

                              {/* 图标和文字 */}
                              <motion.div
                                className="relative z-10 flex items-center gap-2"
                                animate={
                                  isGenerating
                                    ? {
                                        rotate: [0, 360],
                                      }
                                    : {}
                                }
                                transition={{
                                  duration: 2,
                                  repeat: isGenerating ? Infinity : 0,
                                  ease: "linear",
                                }}
                              >
                                <Send className="h-4 w-4" />
                              </motion.div>
                              <span className="relative z-10">
                                {isGenerating ? "生成中..." : "生成"}
                              </span>

                              {/* 加载状态的脉冲效果 */}
                              {isGenerating && (
                                <motion.div
                                  className="absolute inset-0 bg-gradient-to-r from-blue-400/50 to-purple-500/50"
                                  animate={{ opacity: [0.5, 1, 0.5] }}
                                  transition={{
                                    duration: 1.5,
                                    repeat: Infinity,
                                  }}
                                />
                              )}
                            </motion.button>
                          </div>
                        </form>
                      </motion.div>
                    )}
                  </>
                )}
              </div>
            ) : (
              <motion.div
                className="group relative flex h-full w-full cursor-pointer items-center justify-center overflow-hidden rounded-2xl border-2 border-dashed border-gray-300 bg-gradient-to-br from-white/60 to-gray-50/60 p-12 backdrop-blur-md transition-all duration-300 hover:border-blue-400 hover:from-blue-50/60 hover:to-indigo-50/60"
                onClick={() => fileInputRef.current?.click()}
                onDrop={handleDrop}
                onDragOver={(e) => e.preventDefault()}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                {/* 背景动画效果 */}
                <motion.div
                  className="absolute inset-0 bg-gradient-to-r from-blue-400/10 via-purple-400/10 to-pink-400/10"
                  initial={{ opacity: 0, rotate: 0 }}
                  whileHover={{ opacity: 1, rotate: 180 }}
                  transition={{ duration: 0.8 }}
                />

                {/* 浮动粒子效果 */}
                <div className="absolute inset-0 overflow-hidden">
                  {Array.from({ length: 6 }, (_, i) => (
                    <motion.div
                      key={i}
                      className="absolute h-2 w-2 rounded-full bg-blue-400/30"
                      style={{
                        left: `${20 + i * 15}%`,
                        top: `${30 + (i % 2) * 40}%`,
                      }}
                      animate={{
                        y: [-10, 10, -10],
                        opacity: [0.3, 0.8, 0.3],
                      }}
                      transition={{
                        duration: 2 + i * 0.5,
                        repeat: Infinity,
                        ease: "easeInOut",
                      }}
                    />
                  ))}
                </div>

                <div className="relative z-10 space-y-6 text-center">
                  {isUploading ? (
                    <>
                      <motion.div
                        className="mx-auto flex h-24 w-24 items-center justify-center rounded-full bg-gradient-to-r from-blue-500 to-purple-600 shadow-lg"
                        animate={{ rotate: 360 }}
                        transition={{
                          duration: 2,
                          repeat: Infinity,
                          ease: "linear",
                        }}
                      >
                        <motion.div
                          className="h-12 w-12 rounded-full border-4 border-white border-t-transparent"
                          animate={{ rotate: -360 }}
                          transition={{
                            duration: 1,
                            repeat: Infinity,
                            ease: "linear",
                          }}
                        />
                      </motion.div>
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.2 }}
                      >
                        <p className="mb-3 text-xl font-semibold text-gray-700">
                          正在上传图片...
                        </p>
                        <p className="text-base text-gray-500">
                          请稍候，正在上传到云存储
                        </p>
                      </motion.div>
                    </>
                  ) : (
                    <>
                      <motion.div
                        className="relative mx-auto flex h-28 w-28 items-center justify-center rounded-full bg-gradient-to-r from-gray-100 to-gray-200 shadow-xl transition-all duration-300 group-hover:from-blue-100 group-hover:to-indigo-100 group-hover:shadow-2xl"
                        whileHover={{
                          rotate: 10,
                          scale: 1.1,
                        }}
                        transition={{ duration: 0.3 }}
                      >
                        {/* 内圈光效 */}
                        <motion.div
                          className="absolute inset-2 rounded-full bg-gradient-to-r from-white/50 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100"
                          initial={{ rotate: 0 }}
                          whileHover={{ rotate: 180 }}
                          transition={{ duration: 0.8 }}
                        />

                        {/* 上传图标 */}
                        <motion.div
                          className="relative z-10"
                          whileHover={{ y: -2 }}
                          transition={{ duration: 0.2 }}
                        >
                          <Upload className="h-14 w-14 text-gray-400 transition-all duration-300 group-hover:text-blue-500 group-hover:drop-shadow-lg" />
                        </motion.div>

                        {/* 脉冲环效果 */}
                        <motion.div
                          className="absolute inset-0 rounded-full border-2 border-blue-400/30"
                          initial={{ scale: 1, opacity: 0 }}
                          whileHover={{
                            scale: [1, 1.2, 1],
                            opacity: [0, 0.6, 0],
                          }}
                          transition={{
                            duration: 1.5,
                            repeat: Infinity,
                            ease: "easeInOut",
                          }}
                        />
                      </motion.div>
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.1 }}
                      >
                        <p className="mb-3 text-xl font-semibold text-gray-700 transition-colors duration-300 group-hover:text-blue-700">
                          上传图片开始编辑
                        </p>
                        <p className="text-base text-gray-500 transition-colors duration-300 group-hover:text-blue-600">
                          点击上传或拖拽图片到此处
                        </p>
                        <motion.p
                          className="mt-2 text-sm text-gray-400"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          transition={{ delay: 0.3 }}
                        >
                          支持 JPG、PNG、WebP 格式
                        </motion.p>
                      </motion.div>
                    </>
                  )}
                </div>
              </motion.div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
