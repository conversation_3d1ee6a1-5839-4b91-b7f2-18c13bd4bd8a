"use client";

import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { Button } from '@/components/ui/button';
import { 
  Send, 
  Sparkles, 
  Loader2, 
  Wand2,
  Palette,
  Sun,
  Contrast,
  Zap
} from 'lucide-react';

interface GlobalEditInputProps {
  onSubmit: (prompt: string) => void;
  isProcessing?: boolean;
  disabled?: boolean;
  placeholder?: string;
  className?: string;
}

const QUICK_PROMPTS = [
  {
    id: 'enhance',
    label: '增强细节',
    prompt: '增强图片细节，提高清晰度和锐度',
    icon: Sparkles,
    color: 'from-blue-500 to-purple-500'
  },
  {
    id: 'brighten',
    label: '调亮图片',
    prompt: '调亮图片，增加亮度和对比度',
    icon: Sun,
    color: 'from-yellow-500 to-orange-500'
  },
  {
    id: 'colorize',
    label: '色彩增强',
    prompt: '增强色彩饱和度，让颜色更鲜艳',
    icon: Palette,
    color: 'from-pink-500 to-red-500'
  },
  {
    id: 'contrast',
    label: '调整对比度',
    prompt: '调整图片对比度，让明暗更分明',
    icon: Contrast,
    color: 'from-gray-500 to-gray-700'
  },
  {
    id: 'artistic',
    label: '艺术化',
    prompt: '将图片转换为艺术风格，增加艺术感',
    icon: Wand2,
    color: 'from-purple-500 to-indigo-500'
  },
  {
    id: 'professional',
    label: '专业修图',
    prompt: '专业级图片修饰，优化整体效果',
    icon: Zap,
    color: 'from-green-500 to-teal-500'
  }
];

export function GlobalEditInput({ 
  onSubmit, 
  isProcessing = false, 
  disabled = false,
  placeholder = "描述你想要的全局调整效果...",
  className = "" 
}: GlobalEditInputProps) {
  const [prompt, setPrompt] = useState('');
  const [showQuickPrompts, setShowQuickPrompts] = useState(false);

  const handleSubmit = useCallback((e: React.FormEvent) => {
    e.preventDefault();
    if (prompt.trim() && !isProcessing && !disabled) {
      onSubmit(prompt.trim());
      setPrompt('');
      setShowQuickPrompts(false);
    }
  }, [prompt, onSubmit, isProcessing, disabled]);

  const handleQuickPrompt = useCallback((quickPrompt: typeof QUICK_PROMPTS[0]) => {
    setPrompt(quickPrompt.prompt);
    setShowQuickPrompts(false);
    onSubmit(quickPrompt.prompt);
  }, [onSubmit]);

  const handleInputFocus = useCallback(() => {
    setShowQuickPrompts(true);
  }, []);

  const handleInputBlur = useCallback(() => {
    // 延迟隐藏，以便点击快捷按钮
    setTimeout(() => setShowQuickPrompts(false), 200);
  }, []);

  return (
    <div className={`relative ${className}`}>
      {/* 主输入框 */}
      <form onSubmit={handleSubmit} className="relative">
        <div className="relative">
          <input
            type="text"
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            onFocus={handleInputFocus}
            onBlur={handleInputBlur}
            placeholder={placeholder}
            disabled={disabled || isProcessing}
            className="w-full h-14 pl-6 pr-16 rounded-2xl border-2 border-gray-200 bg-white/90 backdrop-blur-sm text-base placeholder-gray-500 transition-all duration-200 focus:border-blue-400 focus:ring-4 focus:ring-blue-100 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed"
          />
          
          {/* 发送按钮 */}
          <Button
            type="submit"
            size="sm"
            disabled={!prompt.trim() || isProcessing || disabled}
            className="absolute right-2 top-2 h-10 w-10 p-0 rounded-xl bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 disabled:opacity-50"
          >
            {isProcessing ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Send className="h-4 w-4" />
            )}
          </Button>
        </div>
      </form>

      {/* 快捷指令面板 */}
      <AnimatePresence>
        {showQuickPrompts && (
          <motion.div
            initial={{ opacity: 0, y: 10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className="absolute top-full left-0 right-0 mt-2 z-50"
          >
            <div className="bg-white/95 backdrop-blur-md rounded-2xl border border-gray-200 shadow-xl p-4">
              <div className="mb-3">
                <h4 className="text-sm font-semibold text-gray-700 mb-1">快捷指令</h4>
                <p className="text-xs text-gray-500">点击快速应用常用的编辑效果</p>
              </div>
              
              <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
                {QUICK_PROMPTS.map((quickPrompt) => {
                  const Icon = quickPrompt.icon;
                  return (
                    <motion.button
                      key={quickPrompt.id}
                      onClick={() => handleQuickPrompt(quickPrompt)}
                      disabled={disabled || isProcessing}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      className="group relative p-3 rounded-xl border border-gray-200 bg-white hover:bg-gray-50 transition-all duration-200 text-left disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <div className="flex items-center gap-2 mb-1">
                        <div className={`p-1.5 rounded-lg bg-gradient-to-r ${quickPrompt.color}`}>
                          <Icon className="h-3 w-3 text-white" />
                        </div>
                        <span className="text-sm font-medium text-gray-800">
                          {quickPrompt.label}
                        </span>
                      </div>
                      <p className="text-xs text-gray-500 line-clamp-2">
                        {quickPrompt.prompt}
                      </p>
                      
                      {/* 悬停效果 */}
                      <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                    </motion.button>
                  );
                })}
              </div>
              
              {/* 自定义提示 */}
              <div className="mt-3 pt-3 border-t border-gray-100">
                <p className="text-xs text-gray-500 text-center">
                  或者在上方输入框中描述你想要的具体效果
                </p>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 处理状态指示 */}
      {isProcessing && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="absolute top-full left-0 right-0 mt-2 z-40"
        >
          <div className="bg-blue-50 border border-blue-200 rounded-xl p-3">
            <div className="flex items-center gap-3">
              <div className="flex-shrink-0">
                <div className="h-6 w-6 rounded-full bg-blue-500 flex items-center justify-center">
                  <Loader2 className="h-3 w-3 text-white animate-spin" />
                </div>
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-blue-800">正在处理图片</p>
                <p className="text-xs text-blue-600">AI正在根据你的指令调整图片...</p>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
}
