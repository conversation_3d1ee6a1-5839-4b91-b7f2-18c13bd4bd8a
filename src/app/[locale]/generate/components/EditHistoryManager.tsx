"use client";

import React, { useState, useCallback, useEffect } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { Button } from '@/components/ui/button';
import { 
  Undo, 
  Redo, 
  RotateCcw, 
  Clock, 
  Eye,
  Trash2,
  <PERSON><PERSON><PERSON><PERSON>gle,
  CheckCircle,
  XCircle
} from 'lucide-react';

interface EditStep {
  id: string;
  imageUrl: string;
  prompt: string;
  timestamp: Date;
  type: 'global' | 'region';
  area?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
}

interface EditHistoryManagerProps {
  originalImage: string;
  currentStep: number;
  editHistory: EditStep[];
  onUndo: () => void;
  onRedo: () => void;
  onResetToOriginal: () => void;
  onJumpToStep: (stepIndex: number) => void;
  onClearHistory: () => void;
  maxSteps?: number;
  className?: string;
}

export function EditHistoryManager({
  originalImage,
  currentStep,
  editHistory,
  onUndo,
  onRedo,
  onResetToOriginal,
  onJumpToStep,
  onClearHistory,
  maxSteps = 4,
  className = ""
}: EditHistoryManagerProps) {
  const [showHistory, setShowHistory] = useState(false);
  const [previewStep, setPreviewStep] = useState<number | null>(null);

  const canUndo = currentStep > 0;
  const canRedo = currentStep < editHistory.length;
  const isAtLimit = editHistory.length >= maxSteps;

  const formatTime = useCallback((date: Date) => {
    return date.toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  }, []);

  const getStepImage = useCallback((stepIndex: number) => {
    if (stepIndex === -1) return originalImage;
    return editHistory[stepIndex]?.imageUrl || originalImage;
  }, [originalImage, editHistory]);

  return (
    <div className={`bg-white/90 backdrop-blur-sm rounded-2xl border border-gray-200 shadow-lg ${className}`}>
      {/* 主控制栏 */}
      <div className="p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <Clock className="h-5 w-5 text-gray-600" />
            <span className="text-sm font-medium text-gray-800">编辑历史</span>
            <span className="text-xs text-gray-500">
              ({editHistory.length}/{maxSteps})
            </span>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowHistory(!showHistory)}
            className="text-xs"
          >
            {showHistory ? '收起' : '展开'}
          </Button>
        </div>

        {/* 快捷操作按钮 */}
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={onUndo}
            disabled={!canUndo}
            className="flex-1"
          >
            <Undo className="h-4 w-4 mr-1" />
            撤回
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={onRedo}
            disabled={!canRedo}
            className="flex-1"
          >
            <Redo className="h-4 w-4 mr-1" />
            重做
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={onResetToOriginal}
            className="flex-1"
          >
            <RotateCcw className="h-4 w-4 mr-1" />
            重置
          </Button>
        </div>

        {/* 步数限制警告 */}
        {isAtLimit && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-3 p-3 bg-amber-50 border border-amber-200 rounded-lg"
          >
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-4 w-4 text-amber-600" />
              <span className="text-sm text-amber-800">
                已达到最大编辑步数限制 ({maxSteps} 步)
              </span>
            </div>
          </motion.div>
        )}
      </div>

      {/* 详细历史记录 */}
      <AnimatePresence>
        {showHistory && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="border-t border-gray-100 overflow-hidden"
          >
            <div className="p-4 space-y-3 max-h-80 overflow-y-auto">
              {/* 原始图片 */}
              <motion.div
                className={`flex items-center gap-3 p-3 rounded-lg border transition-all duration-200 cursor-pointer ${
                  currentStep === -1 
                    ? 'border-blue-500 bg-blue-50' 
                    : 'border-gray-200 bg-white hover:bg-gray-50'
                }`}
                onClick={() => onJumpToStep(-1)}
                onMouseEnter={() => setPreviewStep(-1)}
                onMouseLeave={() => setPreviewStep(null)}
                whileHover={{ scale: 1.01 }}
              >
                <div className="flex-shrink-0">
                  <img
                    src={originalImage}
                    alt="原始图片"
                    className="w-12 h-12 rounded-lg object-cover border border-gray-200"
                  />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium text-gray-800">原始图片</span>
                    {currentStep === -1 && (
                      <CheckCircle className="h-4 w-4 text-blue-500" />
                    )}
                  </div>
                  <p className="text-xs text-gray-500">未经编辑的原始图片</p>
                </div>
              </motion.div>

              {/* 编辑步骤 */}
              {editHistory.map((step, index) => (
                <motion.div
                  key={step.id}
                  className={`flex items-center gap-3 p-3 rounded-lg border transition-all duration-200 cursor-pointer ${
                    currentStep === index 
                      ? 'border-blue-500 bg-blue-50' 
                      : 'border-gray-200 bg-white hover:bg-gray-50'
                  }`}
                  onClick={() => onJumpToStep(index)}
                  onMouseEnter={() => setPreviewStep(index)}
                  onMouseLeave={() => setPreviewStep(null)}
                  whileHover={{ scale: 1.01 }}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <div className="flex-shrink-0">
                    <img
                      src={step.imageUrl}
                      alt={`编辑步骤 ${index + 1}`}
                      className="w-12 h-12 rounded-lg object-cover border border-gray-200"
                    />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium text-gray-800">
                        步骤 {index + 1}
                      </span>
                      <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${
                        step.type === 'global' 
                          ? 'bg-green-100 text-green-700' 
                          : 'bg-purple-100 text-purple-700'
                      }`}>
                        {step.type === 'global' ? '全局' : '区域'}
                      </span>
                      {currentStep === index && (
                        <CheckCircle className="h-4 w-4 text-blue-500" />
                      )}
                    </div>
                    <p className="text-xs text-gray-500 line-clamp-1 mt-1">
                      {step.prompt}
                    </p>
                    <p className="text-xs text-gray-400 mt-1">
                      {formatTime(step.timestamp)}
                    </p>
                  </div>
                  <div className="flex-shrink-0">
                    <Eye className="h-4 w-4 text-gray-400" />
                  </div>
                </motion.div>
              ))}

              {/* 清空历史按钮 */}
              {editHistory.length > 0 && (
                <div className="pt-3 border-t border-gray-100">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={onClearHistory}
                    className="w-full text-red-600 border-red-200 hover:bg-red-50"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    清空编辑历史
                  </Button>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 预览弹窗 */}
      <AnimatePresence>
        {previewStep !== null && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setPreviewStep(null)}
          >
            <motion.div
              initial={{ scale: 0.9 }}
              animate={{ scale: 1 }}
              exit={{ scale: 0.9 }}
              className="bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[80vh] overflow-hidden"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="p-4 border-b border-gray-100">
                <h3 className="text-lg font-semibold text-gray-800">
                  {previewStep === -1 ? '原始图片' : `编辑步骤 ${previewStep + 1}`}
                </h3>
                {previewStep !== -1 && editHistory[previewStep] && (
                  <p className="text-sm text-gray-500 mt-1">
                    {editHistory[previewStep].prompt}
                  </p>
                )}
              </div>
              <div className="p-4">
                <img
                  src={getStepImage(previewStep)}
                  alt="预览"
                  className="w-full h-auto rounded-lg border border-gray-200"
                />
              </div>
              <div className="p-4 border-t border-gray-100 flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => setPreviewStep(null)}
                >
                  关闭
                </Button>
                <Button
                  onClick={() => {
                    onJumpToStep(previewStep);
                    setPreviewStep(null);
                  }}
                  className="bg-blue-500 hover:bg-blue-600 text-white"
                >
                  跳转到此步骤
                </Button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
