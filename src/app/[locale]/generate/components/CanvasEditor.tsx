"use client";

import React, { useState, useCallback, useRef } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { InteractiveCanvas } from './InteractiveCanvas';
import { GlobalEditInput } from './GlobalEditInput';
import { RegionEditPanel } from './RegionEditPanel';
import { QuickActionsPanel } from './QuickActionsPanel';
import { EditHistoryManager } from './EditHistoryManager';
import { Button } from '@/components/ui/button';
import { 
  Download, 
  Share2, 
  Save,
  Maximize2,
  Minimize2,
  Settings
} from 'lucide-react';

interface SelectionArea {
  x: number;
  y: number;
  width: number;
  height: number;
}

interface EditStep {
  id: string;
  imageUrl: string;
  prompt: string;
  timestamp: Date;
  type: 'global' | 'region';
  area?: SelectionArea;
}

interface CanvasEditorProps {
  initialImage: string;
  onEdit: (prompt: string, area?: SelectionArea) => Promise<string>;
  onSave?: (imageUrl: string) => void;
  onDownload?: (imageUrl: string) => void;
  isProcessing?: boolean;
  className?: string;
}

export function CanvasEditor({
  initialImage,
  onEdit,
  onSave,
  onDownload,
  isProcessing = false,
  className = ""
}: CanvasEditorProps) {
  const [currentImage, setCurrentImage] = useState(initialImage);
  const [selectedArea, setSelectedArea] = useState<SelectionArea | null>(null);
  const [showRegionEdit, setShowRegionEdit] = useState(false);
  const [editHistory, setEditHistory] = useState<EditStep[]>([]);
  const [currentStep, setCurrentStep] = useState(-1); // -1 表示原始图片
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showSettings, setShowSettings] = useState(false);

  const maxSteps = 4;

  // 处理全局编辑
  const handleGlobalEdit = useCallback(async (prompt: string) => {
    if (editHistory.length >= maxSteps) {
      // 如果达到最大步数，移除最早的编辑
      setEditHistory(prev => prev.slice(1));
      setCurrentStep(prev => prev - 1);
    }

    try {
      const newImageUrl = await onEdit(prompt);
      
      const newStep: EditStep = {
        id: Date.now().toString(),
        imageUrl: newImageUrl,
        prompt,
        timestamp: new Date(),
        type: 'global'
      };

      // 如果当前不在最新步骤，需要截断后续历史
      const newHistory = editHistory.slice(0, currentStep + 1);
      newHistory.push(newStep);
      
      setEditHistory(newHistory);
      setCurrentStep(newHistory.length - 1);
      setCurrentImage(newImageUrl);
    } catch (error) {
      console.error('Global edit failed:', error);
    }
  }, [onEdit, editHistory, currentStep, maxSteps]);

  // 处理区域编辑
  const handleRegionEdit = useCallback(async (prompt: string, area: SelectionArea) => {
    if (editHistory.length >= maxSteps) {
      setEditHistory(prev => prev.slice(1));
      setCurrentStep(prev => prev - 1);
    }

    try {
      const newImageUrl = await onEdit(prompt, area);
      
      const newStep: EditStep = {
        id: Date.now().toString(),
        imageUrl: newImageUrl,
        prompt,
        timestamp: new Date(),
        type: 'region',
        area
      };

      const newHistory = editHistory.slice(0, currentStep + 1);
      newHistory.push(newStep);
      
      setEditHistory(newHistory);
      setCurrentStep(newHistory.length - 1);
      setCurrentImage(newImageUrl);
      setShowRegionEdit(false);
      setSelectedArea(null);
    } catch (error) {
      console.error('Region edit failed:', error);
    }
  }, [onEdit, editHistory, currentStep, maxSteps]);

  // 处理区域选择
  const handleAreaSelect = useCallback((area: SelectionArea) => {
    setSelectedArea(area);
    setShowRegionEdit(true);
  }, []);

  // 撤回操作
  const handleUndo = useCallback(() => {
    if (currentStep > -1) {
      const newStep = currentStep - 1;
      setCurrentStep(newStep);
      setCurrentImage(newStep === -1 ? initialImage : editHistory[newStep].imageUrl);
    }
  }, [currentStep, initialImage, editHistory]);

  // 重做操作
  const handleRedo = useCallback(() => {
    if (currentStep < editHistory.length - 1) {
      const newStep = currentStep + 1;
      setCurrentStep(newStep);
      setCurrentImage(editHistory[newStep].imageUrl);
    }
  }, [currentStep, editHistory]);

  // 重置到原始图片
  const handleResetToOriginal = useCallback(() => {
    setCurrentStep(-1);
    setCurrentImage(initialImage);
  }, [initialImage]);

  // 跳转到指定步骤
  const handleJumpToStep = useCallback((stepIndex: number) => {
    setCurrentStep(stepIndex);
    setCurrentImage(stepIndex === -1 ? initialImage : editHistory[stepIndex].imageUrl);
  }, [initialImage, editHistory]);

  // 清空编辑历史
  const handleClearHistory = useCallback(() => {
    setEditHistory([]);
    setCurrentStep(-1);
    setCurrentImage(initialImage);
  }, [initialImage]);

  // 下载图片
  const handleDownload = useCallback(() => {
    onDownload?.(currentImage);
  }, [currentImage, onDownload]);

  // 保存图片
  const handleSave = useCallback(() => {
    onSave?.(currentImage);
  }, [currentImage, onSave]);

  return (
    <div className={`relative ${isFullscreen ? 'fixed inset-0 z-50 bg-white' : ''} ${className}`}>
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 h-full">
        {/* 左侧面板 - 工具和历史 */}
        <div className="lg:col-span-1 space-y-4">
          {/* 快捷操作面板 */}
          <QuickActionsPanel
            onActionSelect={handleGlobalEdit}
            isProcessing={isProcessing}
          />

          {/* 编辑历史管理 */}
          <EditHistoryManager
            originalImage={initialImage}
            currentStep={currentStep}
            editHistory={editHistory}
            onUndo={handleUndo}
            onRedo={handleRedo}
            onResetToOriginal={handleResetToOriginal}
            onJumpToStep={handleJumpToStep}
            onClearHistory={handleClearHistory}
            maxSteps={maxSteps}
          />
        </div>

        {/* 中间 - Canvas编辑区域 */}
        <div className="lg:col-span-2 space-y-4">
          {/* 顶部工具栏 */}
          <div className="flex items-center justify-between p-4 bg-white/90 backdrop-blur-sm rounded-2xl border border-gray-200 shadow-lg">
            <div className="flex items-center gap-2">
              <h2 className="text-lg font-semibold text-gray-800">Canvas 编辑器</h2>
              <span className="text-sm text-gray-500">
                步骤 {currentStep + 1}/{maxSteps + 1}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsFullscreen(!isFullscreen)}
                className="h-8 w-8 p-0"
              >
                {isFullscreen ? (
                  <Minimize2 className="h-4 w-4" />
                ) : (
                  <Maximize2 className="h-4 w-4" />
                )}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowSettings(!showSettings)}
                className="h-8 w-8 p-0"
              >
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Canvas */}
          <InteractiveCanvas
            imageUrl={currentImage}
            onAreaSelect={handleAreaSelect}
            onGlobalEdit={() => {}} // 全局编辑通过下方输入框处理
            className="min-h-[500px]"
          />

          {/* 全局编辑输入框 */}
          <GlobalEditInput
            onSubmit={handleGlobalEdit}
            isProcessing={isProcessing}
            placeholder="描述你想要的全局调整效果..."
          />
        </div>

        {/* 右侧面板 - 操作和信息 */}
        <div className="lg:col-span-1 space-y-4">
          {/* 操作按钮 */}
          <div className="bg-white/90 backdrop-blur-sm rounded-2xl border border-gray-200 shadow-lg p-4">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">操作</h3>
            <div className="space-y-3">
              <Button
                onClick={handleDownload}
                className="w-full bg-gradient-to-r from-green-500 to-teal-500 hover:from-green-600 hover:to-teal-600"
              >
                <Download className="h-4 w-4 mr-2" />
                下载图片
              </Button>
              <Button
                onClick={handleSave}
                variant="outline"
                className="w-full"
              >
                <Save className="h-4 w-4 mr-2" />
                保存到相册
              </Button>
              <Button
                variant="outline"
                className="w-full"
              >
                <Share2 className="h-4 w-4 mr-2" />
                分享图片
              </Button>
            </div>
          </div>

          {/* 编辑信息 */}
          <div className="bg-white/90 backdrop-blur-sm rounded-2xl border border-gray-200 shadow-lg p-4">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">编辑信息</h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">当前步骤:</span>
                <span className="font-medium">
                  {currentStep === -1 ? '原始图片' : `步骤 ${currentStep + 1}`}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">总编辑数:</span>
                <span className="font-medium">{editHistory.length}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">剩余步数:</span>
                <span className="font-medium">{maxSteps - editHistory.length}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 区域编辑面板 */}
      {showRegionEdit && selectedArea && (
        <RegionEditPanel
          selection={selectedArea}
          onEdit={handleRegionEdit}
          onClose={() => {
            setShowRegionEdit(false);
            setSelectedArea(null);
          }}
          isProcessing={isProcessing}
        />
      )}
    </div>
  );
}
