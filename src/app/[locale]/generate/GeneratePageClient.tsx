"use client";

import { useState, useCallback } from "react";
import { useLocale } from "next-intl";
import { motion, AnimatePresence } from "motion/react";
import { Button } from "@/components/ui/button";
import { BackgroundBlurs } from "../components/BackgroundBlurs";
import { AlertCircle, CheckCircle, LogIn, Home } from "lucide-react";
import { api } from "@/trpc/react";
import type { Session } from "next-auth";
import { signIn } from "next-auth/react";
import { SimpleImageEditor } from "./components/SimpleImageEditor";

interface GeneratePageClientProps {
  session: Session | null;
}

export function GeneratePageClient({ session }: GeneratePageClientProps) {
  const locale = useLocale();
  const [isProcessing, setIsProcessing] = useState(false);
  const [toasts, setToasts] = useState<
    Array<{ id: string; type: "success" | "error"; message: string }>
  >([]);
  const [currentImageUrl, setCurrentImageUrl] = useState<string | null>(null);

  // 获取用户积分信息
  const { data: userWithCredits, refetch: refetchCredits } =
    api.credits.getUserWithCredits.useQuery(undefined, { enabled: !!session });

  const { mutateAsync } = api.creation.create.useMutation();

  const addToast = useCallback((type: "success" | "error", message: string) => {
    const id = Math.random().toString(36).substring(2, 11);
    setToasts((prev) => [...prev, { id, type, message }]);
    setTimeout(() => {
      setToasts((prev) => prev.filter((toast) => toast.id !== id));
    }, 5000);
  }, []);

  const removeToast = useCallback((id: string) => {
    setToasts((prev) => prev.filter((toast) => toast.id !== id));
  }, []);

  // 登录处理函数
  const handleSignIn = useCallback(async () => {
    try {
      await signIn("google", {
        callbackUrl: window.location.href,
        redirect: false,
      });
    } catch (error) {
      console.error("登录失败:", error);
      addToast("error", "登录失败，请重试");
    }
  }, [addToast]);

  // Canvas编辑处理函数
  const handleCanvasEdit = useCallback(
    async (
      prompt: string,
      imageUrl: string,
      _area?: { x: number; y: number; width: number; height: number },
    ) => {
      setIsProcessing(true);

      try {
        // 检查积分是否足够
        if (
          !userWithCredits ||
          !userWithCredits.credits ||
          userWithCredits.credits.availableCredits < 6
        ) {
          addToast("error", "积分不足，每次生成需要6积分");
          return "";
        }

        addToast("success", "开始生成图片...");

        // 调用API进行图片处理
        const response = await mutateAsync({
          imageUrl,
          prompt: prompt.trim(),
          modelId: undefined, // 使用默认模型
          locale: locale,
        });

        if (!response.success) {
          throw new Error("处理失败");
        }

        addToast("success", "图片生成成功！消耗6积分");

        // 刷新积分信息
        void refetchCredits();

        return response.generatedImageUrl;
      } catch (error) {
        console.error("Canvas edit error:", error);
        addToast("error", "图片处理失败，请重试");
        throw error;
      } finally {
        setIsProcessing(false);
      }
    },
    [addToast, locale, mutateAsync, userWithCredits, refetchCredits],
  );

  const downloadImage = useCallback(
    (imageUrl: string, filename = "edited-image") => {
      if (typeof document !== "undefined") {
        const link = document.createElement("a");
        link.href = imageUrl;
        link.download = `${filename}.jpg`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    },
    [],
  );

  // 处理图片上传
  const handleImageUpload = useCallback(
    (imageUrl: string) => {
      setCurrentImageUrl(imageUrl);
      addToast("success", "图片上传成功");
    },
    [addToast],
  );

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    name: "AI Image Editor",
    description: "Professional AI-powered image editing tool",
    applicationCategory: "MultimediaApplication",
    operatingSystem: "Web Browser",
    offers: {
      "@type": "Offer",
      price: "0",
      priceCurrency: "USD",
    },
    featureList: [
      "AI Image Processing",
      "Smart Watermark Removal",
      "Background Replacement",
      "Object Removal",
      "Image Enhancement",
    ],
  };

  return (
    <div className="relative min-h-screen overflow-hidden bg-gradient-to-br from-gray-50 via-white to-blue-50">
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />
      <BackgroundBlurs />

      {/* Animated gradient overlay */}
      <div className="absolute inset-0 animate-pulse bg-gradient-to-br from-rose-100/30 via-orange-100/20 to-amber-100/30" />

      {/* Toast notifications */}
      <div className="fixed right-4 top-4 z-50 space-y-2">
        <AnimatePresence>
          {toasts.map((toast) => (
            <motion.div
              key={toast.id}
              initial={{ opacity: 0, x: 100 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 100 }}
              className={`flex items-center gap-2 rounded-lg border px-4 py-3 shadow-lg backdrop-blur-sm ${
                toast.type === "success"
                  ? "border-green-400/50 bg-green-500/90 text-white"
                  : "border-red-400/50 bg-red-500/90 text-white"
              }`}
              onClick={() => removeToast(toast.id)}
            >
              {toast.type === "success" ? (
                <CheckCircle className="h-4 w-4" />
              ) : (
                <AlertCircle className="h-4 w-4" />
              )}
              <span className="text-sm font-medium">{toast.message}</span>
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {/* 顶部导航栏 */}
      <motion.div
        initial={{ y: -50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5 }}
        className="relative z-10 border-b border-gray-200/50 bg-white/95 backdrop-blur-md"
      >
        <div className="mx-auto flex max-w-7xl items-center justify-between px-6 py-4">
          {/* 左侧：返回首页按钮和标题 */}
          <div className="flex items-center gap-4">
            <motion.button
              onClick={() => (window.location.href = "/")}
              whileHover={{
                scale: 1.05,
                boxShadow: "0 8px 25px rgba(59, 130, 246, 0.4)",
              }}
              whileTap={{ scale: 0.95 }}
              className="group relative flex h-12 w-12 items-center justify-center overflow-hidden rounded-xl bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg transition-all duration-300 hover:from-blue-600 hover:to-indigo-700"
              title="返回首页"
            >
              {/* 背景光效 */}
              <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100" />

              {/* 图标 */}
              <motion.div
                className="relative z-10"
                whileHover={{ rotate: -10 }}
                transition={{ duration: 0.2 }}
              >
                <Home className="h-5 w-5" />
              </motion.div>

              {/* 脉冲效果 */}
              <motion.div
                className="absolute inset-0 rounded-xl bg-white/20"
                initial={{ scale: 0, opacity: 0 }}
                whileHover={{
                  scale: [0, 1.2, 0],
                  opacity: [0, 0.3, 0],
                }}
                transition={{ duration: 0.6 }}
              />
            </motion.button>
            <div>
              <h1 className="text-2xl font-bold text-gray-800">AI图片生成</h1>
              <p className="text-sm text-gray-500">每次生成消耗6积分</p>
            </div>
          </div>

          {/* 右侧：用户信息和积分显示 */}
          {session && userWithCredits ? (
            <div className="flex items-center gap-4">
              {/* 积分显示 */}
              <motion.div
                className="flex items-center gap-2 rounded-xl bg-gradient-to-r from-yellow-100 to-orange-100 px-4 py-2 shadow-sm"
                whileHover={{ scale: 1.02 }}
              >
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gradient-to-r from-yellow-500 to-orange-500 text-white">
                  <span className="text-sm font-bold">💎</span>
                </div>
                <div>
                  <p className="text-sm font-semibold text-gray-800">
                    {userWithCredits.credits?.availableCredits ?? 0} 积分
                  </p>
                  <p className="text-xs text-gray-500">每日赠送12积分</p>
                </div>
              </motion.div>

              {/* 用户头像 */}
              <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gradient-to-r from-blue-500 to-purple-600 text-sm font-bold text-white shadow-md">
                {session.user?.name?.charAt(0) ?? "U"}
              </div>
            </div>
          ) : (
            <Button
              onClick={handleSignIn}
              className="bg-gradient-to-r from-blue-500 to-purple-600 text-white hover:from-blue-600 hover:to-purple-700"
            >
              <LogIn className="mr-2 h-4 w-4" />
              登录使用
            </Button>
          )}
        </div>
      </motion.div>

      {/* 主内容区域 */}
      <div className="flex h-full">
        <div className="flex flex-1 flex-col bg-white/50 backdrop-blur-sm">
          <div className="flex-1 p-8">
            {!session ? (
              <div className="flex h-full items-center justify-center">
                <div className="max-w-md text-center">
                  <div className="mx-auto mb-8 flex h-24 w-24 items-center justify-center rounded-full bg-gradient-to-r from-rose-500 to-orange-500 shadow-lg">
                    <LogIn className="h-12 w-12 text-white" />
                  </div>
                  <h2 className="mb-4 text-3xl font-bold text-gray-800">
                    登录开始使用
                  </h2>
                  <p className="mb-8 text-lg text-gray-600">
                    使用Google账号登录，开始您的AI图片编辑之旅
                  </p>
                  <Button
                    onClick={handleSignIn}
                    size="lg"
                    className="bg-gradient-to-r from-blue-500 to-purple-600 text-white hover:from-blue-600 hover:to-purple-700"
                  >
                    <LogIn className="mr-2 h-5 w-5" />
                    使用Google登录
                  </Button>
                </div>
              </div>
            ) : (
              <SimpleImageEditor
                onEdit={handleCanvasEdit}
                onDownload={downloadImage}
                isProcessing={isProcessing}
                className="h-full"
                initialImageUrl={currentImageUrl}
                onImageUpload={handleImageUpload}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
