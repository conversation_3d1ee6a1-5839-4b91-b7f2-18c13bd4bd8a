"use client";

import { useState, useCallback } from "react";
import { useLocale } from "next-intl";
import { motion, AnimatePresence } from "motion/react";
import { Button } from "@/components/ui/button";
import { BackgroundBlurs } from "../components/BackgroundBlurs";
import { AlertCircle, CheckCircle, LogIn, Home } from "lucide-react";
import { api } from "@/trpc/react";
import { Session } from "next-auth";
import { signIn } from "next-auth/react";
import { SimpleImageEditor } from "./components/SimpleImageEditor";

interface GeneratePageClientProps {
  session: Session | null;
}

export function GeneratePageClient({ session }: GeneratePageClientProps) {
  const locale = useLocale();
  const [isProcessing, setIsProcessing] = useState(false);

  const [generationHistory, setGenerationHistory] = useState<
    Array<{
      id: string;
      originalImage: string;
      resultImage: string;
      prompt: string;
      timestamp: Date;
    }>
  >([]);
  const [toasts, setToasts] = useState<
    Array<{ id: string; type: "success" | "error"; message: string }>
  >([]);
  const [currentImageUrl, setCurrentImageUrl] = useState<string | null>(null);

  // 获取用户积分信息（优先级较低）
  const { data: userWithCredits } = api.credits.getUserWithCredits.useQuery(
    undefined,
    { enabled: !!session },
  );

  const { mutateAsync } = api.creation.create.useMutation();

  const addToast = useCallback((type: "success" | "error", message: string) => {
    const id = Math.random().toString(36).substring(2, 11);
    setToasts((prev) => [...prev, { id, type, message }]);
    setTimeout(() => {
      setToasts((prev) => prev.filter((toast) => toast.id !== id));
    }, 5000);
  }, []);

  const removeToast = useCallback((id: string) => {
    setToasts((prev) => prev.filter((toast) => toast.id !== id));
  }, []);

  // 登录处理函数
  const handleSignIn = useCallback(async () => {
    try {
      await signIn("google", {
        callbackUrl: window.location.href,
        redirect: false,
      });
    } catch (error) {
      console.error("登录失败:", error);
      addToast("error", "登录失败，请重试");
    }
  }, [addToast]);

  // Canvas编辑处理函数
  const handleCanvasEdit = useCallback(
    async (
      prompt: string,
      imageUrl: string,
      _area?: { x: number; y: number; width: number; height: number },
    ) => {
      setIsProcessing(true);

      try {
        // 调用API进行图片处理
        const response = await mutateAsync({
          imageUrl,
          prompt: prompt.trim(),
          modelId: undefined, // 使用默认模型
          locale: locale,
        });

        if (!response.success) {
          throw new Error("处理失败");
        }

        // 添加到历史记录
        const historyItem = {
          id: crypto.randomUUID(),
          originalImage: imageUrl,
          resultImage: response.generatedImageUrl,
          prompt: prompt.trim(),
          timestamp: new Date(),
        };

        setGenerationHistory((prev) => [historyItem, ...prev]);

        return response.generatedImageUrl;
      } catch (error) {
        console.error("Canvas edit error:", error);
        addToast("error", "图片处理失败，请重试");
        throw error;
      } finally {
        setIsProcessing(false);
      }
    },
    [addToast, locale, mutateAsync],
  );

  const downloadImage = useCallback(
    (imageUrl: string, filename = "edited-image") => {
      if (typeof document !== "undefined") {
        const link = document.createElement("a");
        link.href = imageUrl;
        link.download = `${filename}.jpg`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    },
    [],
  );

  // 处理图片上传，添加到历史记录
  const handleImageUpload = useCallback(
    (imageUrl: string) => {
      const historyItem = {
        id: crypto.randomUUID(),
        originalImage: imageUrl,
        resultImage: imageUrl, // 原始图片作为结果图片
        prompt: "原始图片",
        timestamp: new Date(),
      };

      setGenerationHistory((prev) => [historyItem, ...prev]);
      setCurrentImageUrl(imageUrl); // 自动选中新上传的图片
      addToast("success", "图片上传成功");
    },
    [addToast],
  );

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    name: "Flux Pix AI Image Editor",
    description:
      "Professional AI-powered image editing tool with smart features",
    url: "https://fluxpix.ai/generate",
    applicationCategory: "MultimediaApplication",
    operatingSystem: "Web Browser",
    offers: {
      "@type": "Offer",
      price: "0",
      priceCurrency: "USD",
    },
    featureList: [
      "AI Image Processing",
      "Smart Watermark Removal",
      "Background Replacement",
      "Object Removal",
      "Image Enhancement",
    ],
  };

  return (
    <div className="relative min-h-screen w-full bg-gradient-to-br from-rose-50 via-orange-50 to-amber-50">
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />
      <BackgroundBlurs />

      {/* Animated gradient overlay */}
      <div className="absolute inset-0 animate-pulse bg-gradient-to-br from-rose-100/30 via-orange-100/20 to-amber-100/30" />

      {/* Toast notifications */}
      <div className="fixed right-4 top-4 z-50 space-y-2">
        <AnimatePresence>
          {toasts.map((toast) => (
            <motion.div
              key={toast.id}
              initial={{ opacity: 0, x: 100 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 100 }}
              className={`flex items-center gap-2 rounded-lg border px-4 py-3 shadow-lg backdrop-blur-sm ${
                toast.type === "success"
                  ? "border-green-400/50 bg-green-500/90 text-white"
                  : "border-red-400/50 bg-red-500/90 text-white"
              }`}
            >
              {toast.type === "success" ? (
                <CheckCircle className="h-5 w-5" />
              ) : (
                <AlertCircle className="h-5 w-5" />
              )}
              <span className="text-sm font-medium">{toast.message}</span>
              <button
                onClick={() => removeToast(toast.id)}
                className="ml-2 text-white/80 hover:text-white"
              >
                ×
              </button>
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      <div className="flex min-h-screen">
        {/* 左侧历史记录面板 */}
        <motion.div
          initial={{ x: -300, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ duration: 0.5 }}
          className="flex w-80 flex-col border-r border-gray-200/50 bg-white/90 backdrop-blur-sm"
        >
          <div className="border-b border-gray-200/50 p-6">
            <div className="flex items-center gap-4">
              <motion.button
                onClick={() => (window.location.href = "/")}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="flex h-10 w-10 items-center justify-center rounded-lg bg-blue-500 text-white shadow-md hover:bg-blue-600"
                title="返回首页"
              >
                <Home className="h-5 w-5" />
              </motion.button>
              <div>
                <h2 className="text-xl font-bold text-gray-800">生成历史</h2>
                <p className="mt-1 text-sm text-gray-500">您的AI图片编辑记录</p>
              </div>
            </div>
          </div>

          <div className="flex-1 space-y-3 overflow-y-auto p-4">
            {generationHistory.length === 0 ? (
              <div className="mt-12 text-center text-gray-500">
                <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-gray-100">
                  <svg
                    className="h-8 w-8 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                    />
                  </svg>
                </div>
                <p className="font-medium">暂无生成记录</p>
                <p className="mt-2 text-sm">
                  开始编辑图片后，历史记录会显示在这里
                </p>
              </div>
            ) : (
              generationHistory.map((item) => {
                const isSelected = currentImageUrl === item.resultImage;
                const isOriginal = item.prompt === "原始图片";
                return (
                  <motion.div
                    key={item.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    onClick={() => setCurrentImageUrl(item.resultImage)}
                    className={`group relative cursor-pointer rounded-xl transition-all ${
                      isSelected
                        ? "bg-blue-500 shadow-lg ring-2 ring-blue-300 ring-offset-2"
                        : "bg-white/80 hover:bg-white hover:shadow-md"
                    }`}
                  >
                    <div className="relative aspect-square overflow-hidden rounded-xl">
                      <img
                        src={item.resultImage}
                        alt={isOriginal ? "Original image" : "Generated result"}
                        className="h-full w-full object-cover transition-all duration-300 group-hover:scale-105"
                      />

                      {/* 选中状态的覆盖层 */}
                      {isSelected && (
                        <div className="absolute inset-0 flex items-center justify-center bg-blue-500/20">
                          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-white shadow-lg">
                            <svg
                              className="h-5 w-5 text-blue-500"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                            >
                              <path
                                fillRule="evenodd"
                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                clipRule="evenodd"
                              />
                            </svg>
                          </div>
                        </div>
                      )}

                      {/* 原始图片标识 */}
                      {isOriginal && (
                        <div className="absolute left-2 top-2">
                          <div className="flex h-6 w-6 items-center justify-center rounded-full bg-green-500 shadow-sm">
                            <svg
                              className="h-3.5 w-3.5 text-white"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                              />
                            </svg>
                          </div>
                        </div>
                      )}

                      {/* 生成图片的悬停提示图标 */}
                      {!isOriginal && (
                        <div className="absolute right-2 top-2 opacity-0 transition-opacity group-hover:opacity-100">
                          <div
                            className="flex h-6 w-6 items-center justify-center rounded-full bg-black/70 text-white"
                            title={item.prompt}
                          >
                            <svg
                              className="h-3.5 w-3.5"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                              />
                            </svg>
                          </div>
                        </div>
                      )}
                    </div>
                  </motion.div>
                );
              })
            )}
          </div>

          {/* 积分显示 - 优先级较低的位置 */}
          {session && userWithCredits?.credits && (
            <div className="border-t border-gray-200/50 p-4">
              <div className="flex items-center justify-between rounded-lg bg-amber-50/80 px-3 py-2">
                <div className="flex items-center gap-2">
                  <div className="flex h-6 w-6 items-center justify-center rounded-full bg-amber-400">
                    <span className="text-xs font-bold text-white">¥</span>
                  </div>
                  <span className="text-sm text-amber-700">积分余额</span>
                </div>
                <span className="text-sm font-semibold text-amber-800">
                  {userWithCredits.credits.availableCredits}
                </span>
              </div>
            </div>
          )}
        </motion.div>

        {/* 右侧主内容区域 */}
        <div className="flex flex-1 flex-col bg-white/50 backdrop-blur-sm">
          <div className="flex-1 p-8">
            {!session ? (
              <div className="flex h-full items-center justify-center">
                <div className="max-w-md text-center">
                  <div className="mx-auto mb-8 flex h-24 w-24 items-center justify-center rounded-full bg-gradient-to-r from-rose-500 to-orange-500 shadow-lg">
                    <LogIn className="h-12 w-12 text-white" />
                  </div>
                  <h3 className="mb-4 text-3xl font-bold text-gray-800">
                    欢迎使用 Flux Pix
                  </h3>
                  <p className="mb-8 text-lg leading-relaxed text-gray-600">
                    登录后即可开始使用强大的 AI 图片编辑功能，让您的创意无限延伸
                  </p>
                  <motion.div
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Button
                      size="lg"
                      onClick={handleSignIn}
                      className="bg-gradient-to-r from-rose-500 to-orange-500 px-10 py-4 text-lg font-semibold text-white shadow-xl shadow-rose-500/25 transition-all duration-300 hover:from-rose-400 hover:to-orange-400 hover:shadow-2xl"
                    >
                      <LogIn className="mr-3 h-6 w-6" />
                      使用 Google 登录
                    </Button>
                  </motion.div>
                </div>
              </div>
            ) : (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="h-full"
              >
                <SimpleImageEditor
                  onEdit={handleCanvasEdit}
                  onDownload={downloadImage}
                  isProcessing={isProcessing}
                  className="h-full"
                  initialImageUrl={currentImageUrl}
                  onImageUpload={handleImageUpload}
                />
              </motion.div>
            )}
          </div>
        </div>
      </div>

      {/* Toast Notifications */}
      <AnimatePresence>
        {toasts.map((toast) => (
          <motion.div
            key={toast.id}
            initial={{ opacity: 0, y: 50, scale: 0.3 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, scale: 0.5, transition: { duration: 0.2 } }}
            className="fixed bottom-4 right-4 z-50"
          >
            <div
              className={`rounded-lg px-4 py-3 text-white shadow-lg ${
                toast.type === "success" ? "bg-green-500" : "bg-red-500"
              }`}
            >
              <div className="flex items-center gap-2">
                {toast.type === "success" ? (
                  <CheckCircle className="h-5 w-5" />
                ) : (
                  <AlertCircle className="h-5 w-5" />
                )}
                <span className="text-sm font-medium">{toast.message}</span>
              </div>
            </div>
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  );
}
