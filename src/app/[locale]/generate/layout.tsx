import { type Metadata } from "next";
import { generateGenerateMetadata } from "@/lib/metadata";

export async function generateMetadata({
  params
}: {
  params: Promise<{locale: string}>
}): Promise<Metadata> {
  const {locale} = await params;
  return generateGenerateMetadata(locale);
}

export default function GenerateLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <>
      {children}
    </>
  );
}
