import { useTranslations } from 'next-intl';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { BackgroundBlurs } from "../components/BackgroundBlurs";
import { Navbar } from "../components/Navbar";
import { Footer } from "../components/Footer";
import { Metadata } from "next";
import { auth } from "@/server/auth";
import { getTranslations } from 'next-intl/server';

export const metadata: Metadata = {
  title: '隐私政策 - Flux Pix',
  description: 'Flux Pix 隐私政策 - 了解我们如何收集、使用和保护您的个人信息。我们重视您的隐私权和数据安全，严格遵守相关法律法规，保障用户数据隐私。',
  keywords: ['隐私政策', '数据保护', '个人信息', '隐私权', '数据安全', '用户协议', '信息收集', '数据处理'],
  openGraph: {
    title: '隐私政策 - Flux Pix',
    description: 'Flux Pix 隐私政策 - 了解我们如何收集、使用和保护您的个人信息。我们重视您的隐私权和数据安全。',
    type: 'website',
    url: 'https://fluxpix.ai/privacy'
  },
  twitter: {
    card: 'summary',
    title: '隐私政策 - Flux Pix',
    description: 'Flux Pix 隐私政策 - 了解我们如何收集、使用和保护您的个人信息。'
  },
  alternates: {
    canonical: 'https://fluxpix.ai/privacy'
  }
};

export default async function PrivacyPolicy() {
  const t = await getTranslations('privacy');
  const session = await auth();

  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-950 to-slate-900">
      <BackgroundBlurs />

      <Navbar session={session} />

      <div className="container relative z-10 mx-auto px-4 sm:px-6 py-16">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="mb-12 text-center">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-2xl blur-xl" />
              <div className="relative bg-white/5 backdrop-blur-md border border-white/10 rounded-2xl p-8">
                <h1 className="mb-4 text-4xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                  {t('title')}
                </h1>
                <p className="text-xl text-zinc-300">
                  {t('lastUpdated')}: 2024年12月
                </p>
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="space-y-8">
            {/* 信息收集 */}
            <Card className="border-none bg-gradient-to-br from-slate-800/60 via-slate-700/40 to-slate-800/60 backdrop-blur-md border border-white/10 shadow-lg shadow-slate-900/20">
              <CardHeader>
                <CardTitle className="text-xl font-semibold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-gradient-to-r from-blue-400 to-purple-400" />
                  {t('dataCollection.title')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 text-zinc-300">
                <p>{t('dataCollection.intro')}</p>
                <div className="space-y-3">
                  <div>
                    <h4 className="font-medium text-white mb-2">{t('dataCollection.personal.title')}</h4>
                    <ul className="list-disc list-inside space-y-1 text-sm">
                      <li>{t('dataCollection.personal.email')}</li>
                      <li>{t('dataCollection.personal.name')}</li>
                      <li>{t('dataCollection.personal.profile')}</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-medium text-white mb-2">{t('dataCollection.usage.title')}</h4>
                    <ul className="list-disc list-inside space-y-1 text-sm">
                      <li>{t('dataCollection.usage.images')}</li>
                      <li>{t('dataCollection.usage.prompts')}</li>
                      <li>{t('dataCollection.usage.activity')}</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 信息使用 */}
            <Card className="border-none bg-gradient-to-br from-slate-800/60 via-slate-700/40 to-slate-800/60 backdrop-blur-md border border-white/10 shadow-lg shadow-slate-900/20">
              <CardHeader>
                <CardTitle className="text-xl font-semibold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-gradient-to-r from-blue-400 to-purple-400" />
                  {t('dataUsage.title')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 text-zinc-300">
                <p>{t('dataUsage.intro')}</p>
                <ul className="list-disc list-inside space-y-2 text-sm">
                  <li>{t('dataUsage.service')}</li>
                  <li>{t('dataUsage.improvement')}</li>
                  <li>{t('dataUsage.support')}</li>
                  <li>{t('dataUsage.communication')}</li>
                  <li>{t('dataUsage.legal')}</li>
                </ul>
              </CardContent>
            </Card>

            {/* 信息共享 */}
            <Card className="border-none bg-gradient-to-br from-slate-800/60 via-slate-700/40 to-slate-800/60 backdrop-blur-md border border-white/10 shadow-lg shadow-slate-900/20">
              <CardHeader>
                <CardTitle className="text-xl font-semibold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-gradient-to-r from-blue-400 to-purple-400" />
                  {t('dataSharing.title')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 text-zinc-300">
                <p>{t('dataSharing.intro')}</p>
                <ul className="list-disc list-inside space-y-2 text-sm">
                  <li>{t('dataSharing.consent')}</li>
                  <li>{t('dataSharing.providers')}</li>
                  <li>{t('dataSharing.legal')}</li>
                  <li>{t('dataSharing.business')}</li>
                </ul>
              </CardContent>
            </Card>

            {/* 数据安全 */}
            <Card className="border-none bg-gradient-to-br from-slate-800/60 via-slate-700/40 to-slate-800/60 backdrop-blur-md border border-white/10 shadow-lg shadow-slate-900/20">
              <CardHeader>
                <CardTitle className="text-xl font-semibold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-gradient-to-r from-blue-400 to-purple-400" />
                  {t('dataSecurity.title')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 text-zinc-300">
                <p>{t('dataSecurity.intro')}</p>
                <ul className="list-disc list-inside space-y-2 text-sm">
                  <li>{t('dataSecurity.encryption')}</li>
                  <li>{t('dataSecurity.access')}</li>
                  <li>{t('dataSecurity.monitoring')}</li>
                  <li>{t('dataSecurity.backup')}</li>
                </ul>
              </CardContent>
            </Card>

            {/* 用户权利 */}
            <Card className="border-none bg-gradient-to-br from-slate-800/60 via-slate-700/40 to-slate-800/60 backdrop-blur-md border border-white/10 shadow-lg shadow-slate-900/20">
              <CardHeader>
                <CardTitle className="text-xl font-semibold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-gradient-to-r from-blue-400 to-purple-400" />
                  {t('userRights.title')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 text-zinc-300">
                <p>{t('userRights.intro')}</p>
                <ul className="list-disc list-inside space-y-2 text-sm">
                  <li>{t('userRights.access')}</li>
                  <li>{t('userRights.correction')}</li>
                  <li>{t('userRights.deletion')}</li>
                  <li>{t('userRights.portability')}</li>
                  <li>{t('userRights.objection')}</li>
                </ul>
              </CardContent>
            </Card>

            {/* Cookies */}
            <Card className="border-none bg-gradient-to-br from-slate-800/60 via-slate-700/40 to-slate-800/60 backdrop-blur-md border border-white/10 shadow-lg shadow-slate-900/20">
              <CardHeader>
                <CardTitle className="text-xl font-semibold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-gradient-to-r from-blue-400 to-purple-400" />
                  {t('cookies.title')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 text-zinc-300">
                <p>{t('cookies.intro')}</p>
                <div className="space-y-3">
                  <div>
                    <h4 className="font-medium text-white mb-2">{t('cookies.essential.title')}</h4>
                    <p className="text-sm">{t('cookies.essential.description')}</p>
                  </div>
                  <div>
                    <h4 className="font-medium text-white mb-2">{t('cookies.analytics.title')}</h4>
                    <p className="text-sm">{t('cookies.analytics.description')}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 第三方服务 */}
            <Card className="border-none bg-gradient-to-br from-slate-800/60 via-slate-700/40 to-slate-800/60 backdrop-blur-md border border-white/10 shadow-lg shadow-slate-900/20">
              <CardHeader>
                <CardTitle className="text-xl font-semibold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-gradient-to-r from-blue-400 to-purple-400" />
                  {t('thirdParty.title')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 text-zinc-300">
                <p>{t('thirdParty.intro')}</p>
                <ul className="list-disc list-inside space-y-2 text-sm">
                  <li>{t('thirdParty.auth')}</li>
                  <li>{t('thirdParty.storage')}</li>
                  <li>{t('thirdParty.analytics')}</li>
                  <li>{t('thirdParty.payment')}</li>
                </ul>
              </CardContent>
            </Card>

            {/* 数据保留 */}
            <Card className="border-none bg-gradient-to-br from-slate-800/60 via-slate-700/40 to-slate-800/60 backdrop-blur-md border border-white/10 shadow-lg shadow-slate-900/20">
              <CardHeader>
                <CardTitle className="text-xl font-semibold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-gradient-to-r from-blue-400 to-purple-400" />
                  {t('dataRetention.title')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 text-zinc-300">
                <p>{t('dataRetention.intro')}</p>
                <ul className="list-disc list-inside space-y-2 text-sm">
                  <li>{t('dataRetention.account')}</li>
                  <li>{t('dataRetention.images')}</li>
                  <li>{t('dataRetention.logs')}</li>
                </ul>
              </CardContent>
            </Card>

            {/* 儿童隐私 */}
            <Card className="border-none bg-gradient-to-br from-slate-800/60 via-slate-700/40 to-slate-800/60 backdrop-blur-md border border-white/10 shadow-lg shadow-slate-900/20">
              <CardHeader>
                <CardTitle className="text-xl font-semibold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-gradient-to-r from-blue-400 to-purple-400" />
                  {t('childrenPrivacy.title')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 text-zinc-300">
                <p>{t('childrenPrivacy.content')}</p>
              </CardContent>
            </Card>

            {/* 政策更新 */}
            <Card className="border-none bg-gradient-to-br from-slate-800/60 via-slate-700/40 to-slate-800/60 backdrop-blur-md border border-white/10 shadow-lg shadow-slate-900/20">
              <CardHeader>
                <CardTitle className="text-xl font-semibold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-gradient-to-r from-blue-400 to-purple-400" />
                  {t('policyUpdates.title')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 text-zinc-300">
                <p>{t('policyUpdates.content')}</p>
              </CardContent>
            </Card>

            {/* 联系我们 */}
            <Card className="border-none bg-gradient-to-br from-slate-800/60 via-slate-700/40 to-slate-800/60 backdrop-blur-md border border-white/10 shadow-lg shadow-slate-900/20">
              <CardHeader>
                <CardTitle className="text-xl font-semibold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-gradient-to-r from-blue-400 to-purple-400" />
                  {t('contact.title')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 text-zinc-300">
                <p>{t('contact.intro')}</p>
                <div className="space-y-2 text-sm">
                  <div className="bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-lg p-4 border border-blue-500/20">
                    <p className="flex items-center gap-2 mb-2">
                      <strong className="text-blue-300">{t('contact.email.label')}:</strong> 
                      <span className="text-white"><EMAIL></span>
                    </p>
                    <p className="flex items-center gap-2">
                      <strong className="text-blue-300">{t('contact.address.label')}:</strong> 
                      <span className="text-white">{t('contact.address.value')}</span>
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
}
