import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { BackgroundBlurs } from './components/BackgroundBlurs';
import { Navbar } from './components/Navbar';
import { Footer } from './components/Footer';
import { Home, ArrowLeft, Search } from 'lucide-react';
import { Metadata } from 'next';
import { auth } from "@/server/auth";
import { getTranslations } from 'next-intl/server';
import { generateNotFoundMetadata } from '@/lib/metadata';

export async function generateMetadata({
  params
}: {
  params: Promise<{locale: string}>
}): Promise<Metadata> {
  const {locale} = await params;
  return generateNotFoundMetadata(locale);
}

export default async function NotFound({
  params
}: {
  params: Promise<{locale: string}>
}) {
  const {locale} = await params;
  const t = await getTranslations('notFound');
  const session = await auth();

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": "页面未找到 - Flux Pix",
    "description": "404 错误页面",
    "url": "https://fluxpix.ai/404",
    "isPartOf": {
      "@type": "WebSite",
      "name": "Flux Pix",
      "url": "https://fluxpix.ai"
    },
    "mainEntity": {
      "@type": "Thing",
      "name": "404 Error",
      "description": "页面未找到错误"
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-950 to-slate-900">
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData)
        }}
      />
      <BackgroundBlurs />
      
      <Navbar session={session} />

      <div className="container relative z-10 mx-auto px-4 sm:px-6 py-16">
        <div className="max-w-2xl mx-auto text-center">
          {/* 404 Display */}
          <div className="mb-8">
            <h1 className="text-8xl md:text-9xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent mb-4">
              404
            </h1>
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-2xl blur-xl" />
              <div className="relative bg-white/5 backdrop-blur-md border border-white/10 rounded-2xl p-8">
                <h2 className="text-3xl font-bold text-white mb-4">
                  页面未找到
                </h2>
                <p className="text-xl text-zinc-300 mb-8">
                  抱歉，您访问的页面不存在或已被移动。
                </p>
                
                {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Link href="/">
                    <Button 
                      size="lg" 
                      className="w-full sm:w-auto bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"
                    >
                      <Home className="mr-2 h-5 w-5" />
                      返回首页
                    </Button>
                  </Link>
                  <Link href="/generate">
                    <Button 
                      size="lg" 
                      variant="outline"
                      className="w-full sm:w-auto border-white/20 text-white hover:bg-white/10"
                    >
                      <Search className="mr-2 h-5 w-5" />
                      开始编辑
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </div>

          {/* Helpful Links */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-12">
            <div className="bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-6">
              <h3 className="text-lg font-semibold text-white mb-3">常用功能</h3>
              <ul className="space-y-2 text-zinc-300">
                <li>
                  <Link href="/generate" className="hover:text-blue-400 transition-colors">
                    AI 图片编辑
                  </Link>
                </li>
                <li>
                  <Link href="/generate" className="hover:text-blue-400 transition-colors">
                    智能去水印
                  </Link>
                </li>
                <li>
                  <Link href="/generate" className="hover:text-blue-400 transition-colors">
                    背景替换
                  </Link>
                </li>
              </ul>
            </div>
            
            <div className="bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-6">
              <h3 className="text-lg font-semibold text-white mb-3">帮助信息</h3>
              <ul className="space-y-2 text-zinc-300">
                <li>
                  <Link href="/privacy" className="hover:text-blue-400 transition-colors">
                    隐私政策
                  </Link>
                </li>
                <li>
                  <Link href="/terms" className="hover:text-blue-400 transition-colors">
                    服务条款
                  </Link>
                </li>
                <li>
                  <a href="mailto:<EMAIL>" className="hover:text-blue-400 transition-colors">
                    联系支持
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
}