import { Navbar } from "./components/Navbar";
import { HeroSection } from "./components/HeroSection";
import { FeaturesSection } from "./components/FeaturesSection";
import { PricingSection } from "./components/PricingSection";
import { Footer } from "./components/Footer";
import { auth } from "@/server/auth";
import { BackgroundBlurs } from "./components/BackgroundBlurs";
import { Metadata } from "next";
import { generateHomeMetadata } from "@/lib/metadata";

export async function generateMetadata({
  params
}: {
  params: Promise<{locale: string}>
}): Promise<Metadata> {
  const {locale} = await params;
  return generateHomeMetadata(locale);
}

export default async function Home() {
  const session = await auth();

  return (
    <main className="min-h-screen bg-gradient-to-br from-rose-50 via-orange-50 to-amber-50">
      <BackgroundBlurs />
      
      <Navbar session={session} />

      <div className="container relative z-10 mx-auto px-4 sm:px-6">
        <div className="pt-16">
          <HeroSection />
          <FeaturesSection />
          <PricingSection />
        </div>
      </div>

      <Footer />
    </main>
  );
}
