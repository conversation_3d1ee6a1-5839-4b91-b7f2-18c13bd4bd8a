import "@/styles/globals.css";

import { GeistSans } from "geist/font/sans";
import { type Metadata } from "next";
import {NextIntlClientProvider} from 'next-intl';
import {getMessages} from 'next-intl/server';
import {notFound} from 'next/navigation';
import {routing} from '@/i18n/routing';
import { TRPCReactProvider } from "@/trpc/react";
import { SessionProvider } from "next-auth/react";

export async function generateMetadata({
  params
}: {
  params: Promise<{locale: string}>
}): Promise<Metadata> {
  const {locale} = await params;
  
  const isZh = locale === 'zh';
  
  const baseMetadata = {
    title: {
      default: isZh ? 'Flux Pix - AI 图片编辑器' : 'Flux Pix - AI Image Editor',
      template: isZh ? '%s | Flux Pix' : '%s | Flux Pix'
    },
    description: isZh 
      ? 'Flux Pix - 专业的 AI 图片编辑工具，支持智能去水印、背景替换、对象移除。免费在线处理，快速精准，支持 JPG、PNG 格式。立即体验 AI 图片编辑的魅力！'
      : 'Flux Pix - Professional AI-powered image editing tool with smart watermark removal, background replacement, and object removal. Free online processing, fast and accurate, supports JPG, PNG formats. Experience the magic of AI image editing now!',
    keywords: isZh 
      ? ['AI图片编辑', '去水印工具', '背景替换', '对象移除', '图片处理', '人工智能编辑', '在线图片编辑器', '免费图片工具', 'AI修图', '智能抠图']
      : ['AI image editing', 'watermark removal', 'background replacement', 'object removal', 'image processing', 'artificial intelligence editing', 'online image editor', 'free image tools', 'AI photo editing', 'smart cutout'],
    authors: [{ name: 'Flux Pix Team' }],
    creator: 'Flux Pix',
    publisher: 'Flux Pix',
    robots: 'index, follow',
    openGraph: {
      type: 'website',
      siteName: 'Flux Pix',
      title: isZh ? 'Flux Pix - AI 图片编辑器' : 'Flux Pix - AI Image Editor',
      description: isZh 
        ? 'Flux Pix - 专业的 AI 图片编辑工具，支持智能去水印、背景替换、对象移除。免费在线处理，快速精准。'
        : 'Flux Pix - Professional AI-powered image editing tool with smart watermark removal, background replacement, and object removal. Free online processing, fast and accurate.',
      images: ['/og-image.png'],
      locale: isZh ? 'zh_CN' : 'en_US',
      alternateLocale: isZh ? 'en_US' : 'zh_CN'
    },
    twitter: {
      card: 'summary_large_image',
      title: isZh ? 'Flux Pix - AI 图片编辑器' : 'Flux Pix - AI Image Editor',
      description: isZh 
        ? 'Flux Pix - 专业的 AI 图片编辑工具，支持智能去水印、背景替换、对象移除。免费在线处理，快速精准。'
        : 'Flux Pix - Professional AI-powered image editing tool with smart watermark removal, background replacement, and object removal.',
      images: ['/og-image.png']
    },
    icons: [
      { rel: "icon", url: "/favicon.ico" },
      { rel: "apple-touch-icon", url: "/apple-touch-icon.png" },
      { rel: "icon", type: "image/png", sizes: "32x32", url: "/favicon-32x32.png" },
      { rel: "icon", type: "image/png", sizes: "16x16", url: "/favicon-16x16.png" }
    ],
    viewport: 'width=device-width, initial-scale=1',
    themeColor: '#3b82f6',
    alternates: {
      canonical: `https://fluxpix.ai${locale === 'en' ? '' : `/${locale}`}`,
      languages: {
        'en': 'https://fluxpix.ai',
        'zh': 'https://fluxpix.ai/zh',
        'x-default': 'https://fluxpix.ai'
      }
    }
  };
  
  return baseMetadata;
}

export default async function RootLayout({
  children,
  params
}: {
  children: React.ReactNode;
  params: Promise<{locale: string}>;
}) {
  const {locale} = await params;

  if (!routing.locales.includes(locale as 'zh'|'en')) {
    notFound();
  }

    // Providing all messages to the client
  // side is the easiest way to get started
  const messages = await getMessages();

  const isZh = locale === 'zh';

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "Flux Pix",
    "description": isZh ? "使用 AI 技术的专业图片编辑器，支持智能去水印、背景替换、对象移除等功能。" : "Professional AI-powered image editor with smart watermark removal, background replacement, and object removal.",
    "url": "https://fluxpix.ai",
    "applicationCategory": "MultimediaApplication",
    "operatingSystem": "Web",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "author": {
      "@type": "Organization",
      "name": "Flux Pix Team"
    },
    "inLanguage": [
      {
        "@type": "Language",
        "name": "Chinese",
        "alternateName": "zh"
      },
      {
        "@type": "Language", 
        "name": "English",
        "alternateName": "en"
      }
    ],
    "featureList": isZh ? [
      "AI智能去水印",
      "背景替换",
      "对象移除",
      "图片编辑",
      "在线处理"
    ] : [
      "AI Watermark Removal",
      "Background Replacement", 
      "Object Removal",
      "Image Editing",
      "Online Processing"
    ]
  };

  return (
    <html
      lang={locale}
      className={`${GeistSans.variable}`}
      suppressHydrationWarning
    >
      <head>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(structuredData)
          }}
        />
      </head>
      <body>
        <SessionProvider>
          <TRPCReactProvider>
            <NextIntlClientProvider messages={messages}>
              {children}
            </NextIntlClientProvider>
          </TRPCReactProvider>
        </SessionProvider>
      </body>
    </html>
  );
}
