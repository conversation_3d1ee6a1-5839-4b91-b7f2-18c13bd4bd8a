import { useTranslations } from 'next-intl';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { BackgroundBlurs } from "../components/BackgroundBlurs";
import { Navbar } from "../components/Navbar";
import { Footer } from "../components/Footer";
import { Metadata } from "next";
import { auth } from "@/server/auth";
import { getTranslations } from 'next-intl/server';

export const metadata: Metadata = {
  title: '服务条款 - Flux Pix',
  description: 'Flux Pix 服务条款 - 了解使用我们服务的规则和条件。包含用户责任、服务使用协议和法律条款。详细说明用户权利义务，保障双方合法权益。',
  keywords: ['服务条款', '使用协议', '用户协议', '法律条款', '服务规则', '用户责任', '服务条件', '使用规范'],
  openGraph: {
    title: '服务条款 - Flux Pix',
    description: 'Flux Pix 服务条款 - 了解使用我们服务的规则和条件。包含用户责任、服务使用协议和法律条款。',
    type: 'website',
    url: 'https://fluxpix.ai/terms'
  },
  twitter: {
    card: 'summary',
    title: '服务条款 - Flux Pix',
    description: 'Flux Pix 服务条款 - 了解使用我们服务的规则和条件。'
  },
  alternates: {
    canonical: 'https://fluxpix.ai/terms'
  }
};

export default async function TermsOfService() {
  const t = await getTranslations('privacy');
  const session = await auth();

  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-950 to-slate-900">
      <BackgroundBlurs />

      <Navbar session={session} />

      <div className="container relative z-10 mx-auto px-4 sm:px-6 py-16">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="mb-12 text-center">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-2xl blur-xl" />
              <div className="relative bg-white/5 backdrop-blur-md border border-white/10 rounded-2xl p-8">
                <h1 className="mb-4 text-4xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                  {t('title')}
                </h1>
                <p className="text-xl text-zinc-300">
                  {t('lastUpdated')}: 2024年12月
                </p>
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="space-y-8">
            {/* 服务协议 */}
            <Card className="border-none bg-gradient-to-br from-slate-800/60 via-slate-700/40 to-slate-800/60 backdrop-blur-md border border-white/10 shadow-lg shadow-slate-900/20">
              <CardHeader>
                <CardTitle className="text-xl font-semibold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-gradient-to-r from-blue-400 to-purple-400" />
                  {t('serviceAgreement.title')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 text-zinc-300">
                <p>{t('serviceAgreement.intro')}</p>
                <ul className="list-disc list-inside space-y-2 text-sm">
                  <li>{t('serviceAgreement.acceptance')}</li>
                  <li>{t('serviceAgreement.changes')}</li>
                  <li>{t('serviceAgreement.eligibility')}</li>
                </ul>
              </CardContent>
            </Card>

            {/* 服务描述 */}
            <Card className="border-none bg-gradient-to-br from-slate-800/60 via-slate-700/40 to-slate-800/60 backdrop-blur-md border border-white/10 shadow-lg shadow-slate-900/20">
              <CardHeader>
                <CardTitle className="text-xl font-semibold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-gradient-to-r from-blue-400 to-purple-400" />
                  {t('serviceDescription.title')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 text-zinc-300">
                <p>{t('serviceDescription.intro')}</p>
                <ul className="list-disc list-inside space-y-2 text-sm">
                  <li>{t('serviceDescription.editing')}</li>
                  <li>{t('serviceDescription.ai')}</li>
                  <li>{t('serviceDescription.storage')}</li>
                  <li>{t('serviceDescription.support')}</li>
                </ul>
              </CardContent>
            </Card>

            {/* 用户责任 */}
            <Card className="border-none bg-gradient-to-br from-slate-800/60 via-slate-700/40 to-slate-800/60 backdrop-blur-md border border-white/10 shadow-lg shadow-slate-900/20">
              <CardHeader>
                <CardTitle className="text-xl font-semibold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-gradient-to-r from-blue-400 to-purple-400" />
                  {t('userResponsibilities.title')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 text-zinc-300">
                <p>{t('userResponsibilities.intro')}</p>
                <ul className="list-disc list-inside space-y-2 text-sm">
                  <li>{t('userResponsibilities.lawful')}</li>
                  <li>{t('userResponsibilities.rights')}</li>
                  <li>{t('userResponsibilities.content')}</li>
                  <li>{t('userResponsibilities.security')}</li>
                </ul>
              </CardContent>
            </Card>

            {/* 禁止行为 */}
            <Card className="border-none bg-gradient-to-br from-slate-800/60 via-slate-700/40 to-slate-800/60 backdrop-blur-md border border-white/10 shadow-lg shadow-slate-900/20">
              <CardHeader>
                <CardTitle className="text-xl font-semibold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-gradient-to-r from-blue-400 to-purple-400" />
                  {t('prohibitedUse.title')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 text-zinc-300">
                <p>{t('prohibitedUse.intro')}</p>
                <ul className="list-disc list-inside space-y-2 text-sm">
                  <li>{t('prohibitedUse.illegal')}</li>
                  <li>{t('prohibitedUse.harmful')}</li>
                  <li>{t('prohibitedUse.infringing')}</li>
                  <li>{t('prohibitedUse.malicious')}</li>
                  <li>{t('prohibitedUse.spam')}</li>
                </ul>
              </CardContent>
            </Card>

            {/* 知识产权 */}
            <Card className="border-none bg-gradient-to-br from-slate-800/60 via-slate-700/40 to-slate-800/60 backdrop-blur-md border border-white/10 shadow-lg shadow-slate-900/20">
              <CardHeader>
                <CardTitle className="text-xl font-semibold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-gradient-to-r from-blue-400 to-purple-400" />
                  {t('intellectualProperty.title')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 text-zinc-300">
                <p>{t('intellectualProperty.intro')}</p>
                <div className="space-y-3">
                  <div>
                    <h4 className="font-medium text-white mb-2">{t('intellectualProperty.ourRights.title')}</h4>
                    <p className="text-sm">{t('intellectualProperty.ourRights.description')}</p>
                  </div>
                  <div>
                    <h4 className="font-medium text-white mb-2">{t('intellectualProperty.userContent.title')}</h4>
                    <p className="text-sm">{t('intellectualProperty.userContent.description')}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 付费服务 */}
            <Card className="border-none bg-gradient-to-br from-slate-800/60 via-slate-700/40 to-slate-800/60 backdrop-blur-md border border-white/10 shadow-lg shadow-slate-900/20">
              <CardHeader>
                <CardTitle className="text-xl font-semibold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-gradient-to-r from-blue-400 to-purple-400" />
                  {t('paidServices.title')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 text-zinc-300">
                <p>{t('paidServices.intro')}</p>
                <ul className="list-disc list-inside space-y-2 text-sm">
                  <li>{t('paidServices.billing')}</li>
                  <li>{t('paidServices.refunds')}</li>
                  <li>{t('paidServices.cancellation')}</li>
                  <li>{t('paidServices.changes')}</li>
                </ul>
              </CardContent>
            </Card>

            {/* 服务可用性 */}
            <Card className="border-none bg-gradient-to-br from-slate-800/60 via-slate-700/40 to-slate-800/60 backdrop-blur-md border border-white/10 shadow-lg shadow-slate-900/20">
              <CardHeader>
                <CardTitle className="text-xl font-semibold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-gradient-to-r from-blue-400 to-purple-400" />
                  {t('serviceAvailability.title')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 text-zinc-300">
                <p>{t('serviceAvailability.content')}</p>
              </CardContent>
            </Card>

            {/* 免责声明 */}
            <Card className="border-none bg-gradient-to-br from-slate-800/60 via-slate-700/40 to-slate-800/60 backdrop-blur-md border border-white/10 shadow-lg shadow-slate-900/20">
              <CardHeader>
                <CardTitle className="text-xl font-semibold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-gradient-to-r from-blue-400 to-purple-400" />
                  {t('disclaimer.title')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 text-zinc-300">
                <p>{t('disclaimer.content')}</p>
              </CardContent>
            </Card>

            {/* 责任限制 */}
            <Card className="border-none bg-gradient-to-br from-slate-800/60 via-slate-700/40 to-slate-800/60 backdrop-blur-md border border-white/10 shadow-lg shadow-slate-900/20">
              <CardHeader>
                <CardTitle className="text-xl font-semibold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-gradient-to-r from-blue-400 to-purple-400" />
                  {t('limitationOfLiability.title')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 text-zinc-300">
                <p>{t('limitationOfLiability.content')}</p>
              </CardContent>
            </Card>

            {/* 终止服务 */}
            <Card className="border-none bg-gradient-to-br from-slate-800/60 via-slate-700/40 to-slate-800/60 backdrop-blur-md border border-white/10 shadow-lg shadow-slate-900/20">
              <CardHeader>
                <CardTitle className="text-xl font-semibold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-gradient-to-r from-blue-400 to-purple-400" />
                  {t('termination.title')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 text-zinc-300">
                <p>{t('termination.intro')}</p>
                <ul className="list-disc list-inside space-y-2 text-sm">
                  <li>{t('termination.userTermination')}</li>
                  <li>{t('termination.ourTermination')}</li>
                  <li>{t('termination.effect')}</li>
                </ul>
              </CardContent>
            </Card>

            {/* 适用法律 */}
            <Card className="border-none bg-gradient-to-br from-slate-800/60 via-slate-700/40 to-slate-800/60 backdrop-blur-md border border-white/10 shadow-lg shadow-slate-900/20">
              <CardHeader>
                <CardTitle className="text-xl font-semibold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-gradient-to-r from-blue-400 to-purple-400" />
                  {t('governingLaw.title')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 text-zinc-300">
                <p>{t('governingLaw.content')}</p>
              </CardContent>
            </Card>

            {/* 联系信息 */}
            <Card className="border-none bg-gradient-to-br from-slate-800/60 via-slate-700/40 to-slate-800/60 backdrop-blur-md border border-white/10 shadow-lg shadow-slate-900/20">
              <CardHeader>
                <CardTitle className="text-xl font-semibold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-gradient-to-r from-blue-400 to-purple-400" />
                  {t('contact.title')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 text-zinc-300">
                <p>{t('contact.intro')}</p>
                <div className="space-y-2 text-sm">
                  <div className="bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-lg p-4 border border-blue-500/20">
                    <p className="flex items-center gap-2 mb-2">
                      <strong className="text-blue-300">{t('contact.email.label')}:</strong> 
                      <span className="text-white"><EMAIL></span>
                    </p>
                    <p className="flex items-center gap-2">
                      <strong className="text-blue-300">{t('contact.address.label')}:</strong> 
                      <span className="text-white">{t('contact.address.value')}</span>
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
}
