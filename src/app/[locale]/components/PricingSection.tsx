import { Check, X, ArrowRight } from "lucide-react";
import * as motion from "motion/react-client";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ScrollAnimation } from "@/components/ui/scroll-animation";
import { useTranslations } from 'next-intl';
import { Link } from "@/i18n/routing";

interface PricingFeature {
  text: string;
  included: boolean;
}

interface PricingPlanProps {
  title: string;
  price: string;
  description: string;
  features: PricingFeature[];
  isPopular?: boolean;
  buttonText: string;
  credits: string;
  popularLabel: string;
}

function PricingPlan({
  title,
  price,
  description,
  features,
  isPopular = false,
  buttonText,
  credits,
  popularLabel,
}: PricingPlanProps) {
  return (
    <motion.div
      whileHover={{ scale: 1.02 }}
      transition={{ duration: 0.2 }}
      className="relative h-full"
    >
      {isPopular && (
        <div className="absolute -top-3 left-1/2 -translate-x-1/2 rounded-full bg-gradient-to-r from-rose-500 to-orange-500 px-3 py-1 text-xs font-medium text-white z-10">
          {popularLabel}
        </div>
      )}

      <Card className={`border-none backdrop-blur-sm h-full ${
        isPopular
          ? "bg-white/80 border border-rose-200/50 shadow-lg"
          : "bg-white/70 border border-gray-200/50"
      }`}>
        <CardContent className="p-6 space-y-6 h-full flex flex-col">
          {/* Header */}
          <div className="text-center">
            <h3 className="mb-2 text-xl font-bold text-gray-800">{title}</h3>
            <div className="mb-2 text-3xl font-bold text-gray-800">{price}</div>
            <p className="text-sm text-gray-600">{description}</p>
          </div>

          {/* Credits */}
          <div className="rounded-lg bg-rose-50 border border-rose-100 p-3 text-center">
            <span className="font-medium text-gray-800">{credits}</span>
          </div>

          {/* Features */}
          <div className="flex-1">
            <ul className="space-y-3">
              {features.map((feature, index) => (
                <li key={index} className="flex items-start gap-3">
                  {feature.included ? (
                    <Check className="h-5 w-5 text-green-400 mt-0.5 flex-shrink-0" />
                  ) : (
                    <X className="h-5 w-5 text-red-400 mt-0.5 flex-shrink-0" />
                  )}
                  <span
                    className={`text-sm leading-relaxed ${feature.included ? "text-gray-700" : "text-gray-500"}`}
                  >
                    {feature.text}
                  </span>
                </li>
              ))}
            </ul>
          </div>

          {/* Button */}
          <Link href="/pricing">
            <Button
              className={`w-full ${
                isPopular
                  ? "bg-gradient-to-r from-rose-500 to-orange-500 hover:from-rose-400 hover:to-orange-400 text-white"
                  : "bg-gray-100 hover:bg-gray-200 text-gray-800"
              }`}
            >
              {buttonText}
            </Button>
          </Link>
        </CardContent>
      </Card>
    </motion.div>
  );
}

export function PricingSection() {
  const t = useTranslations('pricing');
  
  const freePlanFeatures = [
    { text: t('freePlan.features.dailyCredits'), included: true },
    { text: t('freePlan.features.noAds'), included: false },
    { text: t('freePlan.features.commercialUse'), included: false },
  ];

  const basicPlanFeatures = [
    { text: t('basicPlan.features.monthlyCredits'), included: true },
    { text: t('basicPlan.features.noAds'), included: true },
    { text: t('basicPlan.features.commercialUse'), included: true },
  ];

  const proPlanFeatures = [
    { text: t('proPlan.features.monthlyCredits'), included: true },
    { text: t('proPlan.features.unlimitedPrompts'), included: true },
    { text: t('proPlan.features.commercialUse'), included: true },
  ];

  return (
    <section className="py-16">
      <ScrollAnimation direction="up" className="mb-16 text-center">
        <h2 className="mb-4 text-3xl font-bold text-gray-800">
          {t('sectionTitle')}
        </h2>
        <p className="mx-auto max-w-3xl text-xl text-gray-600">
          {t('sectionSubtitle')}
        </p>
      </ScrollAnimation>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
        <ScrollAnimation direction="up" delay={0.2}>
          <PricingPlan
            title={t('freePlan.title')}
            price={t('freePlan.price')}
            description={t('freePlan.description')}
            features={freePlanFeatures}
            buttonText={t('freePlan.buttonText')}
            credits={t('freePlan.credits')}
            popularLabel={t('popularLabel')}
          />
        </ScrollAnimation>

        <ScrollAnimation direction="up" delay={0.3}>
          <PricingPlan
            title={t('basicPlan.title')}
            price={t('basicPlan.price')}
            description={t('basicPlan.description')}
            features={basicPlanFeatures}
            isPopular={true}
            buttonText={t('basicPlan.buttonText')}
            credits={t('basicPlan.credits')}
            popularLabel={t('popularLabel')}
          />
        </ScrollAnimation>

        <ScrollAnimation direction="up" delay={0.4}>
          <PricingPlan
            title={t('proPlan.title')}
            price={t('proPlan.price')}
            description={t('proPlan.description')}
            features={proPlanFeatures}
            buttonText={t('proPlan.buttonText')}
            credits={t('proPlan.credits')}
            popularLabel={t('popularLabel')}
          />
        </ScrollAnimation>
      </div>
    </section>
  );
}
