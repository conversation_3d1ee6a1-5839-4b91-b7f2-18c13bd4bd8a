"use client";

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Sciss<PERSON> } from "lucide-react";
import * as motion from "motion/react-client";
import { But<PERSON> } from "@/components/ui/button";
import { ScrollAnimation } from "@/components/ui/scroll-animation";
import { useEffect, useState } from "react";
import { useTranslations } from 'next-intl';
import { Link } from "@/i18n/routing";
import { useIsClient } from "@/hooks/useIsClient";

export function HeroSection() {
  const t = useTranslations('hero');
  const [typedText, setTypedText] = useState("");
  const fullText = t('typingText');
  const [typingComplete, setTypingComplete] = useState(false);
  const isClient = useIsClient();
  const mainTextLength = t('typingMainPart').length;

  useEffect(() => {
    if (!isClient) return;

    // 整合的打字效果
    let currentIndex = 0;
    const typingInterval = setInterval(() => {
      if (currentIndex <= fullText.length) {
        setTypedText(fullText.slice(0, currentIndex));
        currentIndex++;
      } else {
        clearInterval(typingInterval);
        setTypingComplete(true);
      }
    }, 100);

    return () => clearInterval(typingInterval);
  }, [fullText, isClient]);

  return (
    <div className="relative flex min-h-[calc(100vh-5rem)] flex-col items-center justify-center py-20">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        className="container relative z-10 mx-auto px-4 text-center sm:px-6 lg:px-8"
      >
        <h1 className="mx-auto max-w-4xl text-4xl font-bold tracking-tight text-gray-800 sm:text-5xl md:text-6xl">
          <span className="block bg-gradient-to-r from-rose-600 to-orange-600 bg-clip-text text-transparent">{t('title')}</span>
          <span className="mt-2 block">
            <span className="inline text-gray-700">{typedText.slice(0, mainTextLength)}</span>
            {typedText.length > mainTextLength && (
              <span className="inline bg-gradient-to-r from-rose-500 to-orange-500 bg-clip-text text-transparent">
                {typedText.slice(mainTextLength)}
              </span>
            )}
            <span
              className={`ml-1 inline-block h-8 w-1 animate-blink bg-rose-500 ${
                typingComplete ? "opacity-0" : "opacity-100"
              }`}
            ></span>
          </span>
        </h1>

        <p className="mx-auto mt-6 max-w-2xl text-center text-lg text-gray-600 sm:text-xl">
          {t('subtitle')}
        </p>

        <div className="mx-auto mt-10 flex max-w-xs flex-col justify-center gap-4 sm:max-w-none sm:flex-row">
          <Button
            size="lg"
            className="bg-gradient-to-r from-rose-500 to-orange-500 text-white shadow-lg shadow-rose-500/25 transition-all duration-300 hover:from-rose-400 hover:to-orange-400 hover:shadow-rose-400/30"
            asChild
          >
            <Link href="/generate">
              {t('getStartedButton')} <ArrowRight className="ml-2" />
            </Link>
          </Button>
        </div>

        <div className="mt-16 grid grid-cols-1 gap-6 sm:grid-cols-3">
          <ScrollAnimation
            direction="up"
            delay={0.3}
            className="rounded-xl bg-white/60 backdrop-blur-sm border border-rose-100/50 p-4"
          >
            <Eraser className="mx-auto mb-3 h-8 w-8 text-rose-500" />
            <h3 className="mb-1 font-medium text-gray-800">{t('features.removeWatermark.title')}</h3>
            <p className="text-sm text-gray-600">
              {t('features.removeWatermark.description')}
            </p>
          </ScrollAnimation>

          <ScrollAnimation
            direction="up"
            delay={0.4}
            className="rounded-xl bg-white/60 backdrop-blur-sm border border-orange-100/50 p-4"
          >
            <Palette className="mx-auto mb-3 h-8 w-8 text-orange-500" />
            <h3 className="mb-1 font-medium text-gray-800">{t('features.backgroundChange.title')}</h3>
            <p className="text-sm text-gray-600">
              {t('features.backgroundChange.description')}
            </p>
          </ScrollAnimation>

          <ScrollAnimation
            direction="up"
            delay={0.5}
            className="rounded-xl bg-white/60 backdrop-blur-sm border border-amber-100/50 p-4"
          >
            <Scissors className="mx-auto mb-3 h-8 w-8 text-amber-600" />
            <h3 className="mb-1 font-medium text-gray-800">{t('features.objectRemoval.title')}</h3>
            <p className="text-sm text-gray-600">
              {t('features.objectRemoval.description')}
            </p>
          </ScrollAnimation>
        </div>
      </motion.div>
    </div>
  );
}
