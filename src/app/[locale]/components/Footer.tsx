import { useTranslations } from 'next-intl';
import { Link } from "@/i18n/routing";

export function Footer() {
  const t = useTranslations('footer');
  
  return (
    <footer className="border-t border-gray-200 bg-white/80 py-12">
      <div className="container mx-auto px-4 sm:px-6">
        <div className="flex flex-col items-center justify-center space-y-4 md:flex-row md:space-x-8 md:space-y-0">
          <a href="#" className="text-sm text-gray-600 hover:text-gray-800">
            {t('features')}
          </a>
          <a href="#" className="text-sm text-gray-600 hover:text-gray-800">
            {t('pricing')}
          </a>
          <a href="#" className="text-sm text-gray-600 hover:text-gray-800">
            {t('about')}
          </a>
          <a href="#" className="text-sm text-gray-600 hover:text-gray-800">
            {t('contact')}
          </a>
          <Link href="/terms" className="text-sm text-gray-600 hover:text-gray-800">
            {t('terms')}
          </Link>
          <Link href="/privacy" className="text-sm text-gray-600 hover:text-gray-800">
            {t('privacy')}
          </Link>
        </div>
        <div className="mt-8 border-t border-gray-200 pt-6 text-center">
          <p className="text-sm text-gray-600">
            &copy; {new Date().getFullYear()} Flux-Pix. {t('copyright')}
          </p>
        </div>
      </div>
    </footer>
  );
}
