'use client';

import {
  Wand<PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  ArrowRight,
} from "lucide-react";
import * as motion from "motion/react-client";
import { ScrollAnimation } from "@/components/ui/scroll-animation";
import { useTranslations } from 'next-intl';
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Link } from "@/i18n/routing";
import { ImageComparison } from "@/components/ui/image-comparison";

interface FeatureCardProps {
  icon: React.ReactNode;
  title: string;
  beforeImage: string;
  afterImage: string;
}

function FeatureCard({
  icon,
  title,
  beforeImage,
  afterImage,
}: FeatureCardProps) {
  return (
    <motion.div
      whileHover={{ scale: 1.03, y: -5 }}
      transition={{ duration: 0.3, type: "spring", stiffness: 300 }}
      className="group"
    >
      <Card className="border-none backdrop-blur-md h-full overflow-hidden relative bg-white/70 border border-rose-200/50 shadow-lg">
        {/* 图片对比区域 */}
        <div className="relative">
          <ImageComparison
            beforeImage={beforeImage}
            afterImage={afterImage}
            title={title}
          />

          {/* 功能名称（提示词）叠加在图片下方 */}
          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-gray-800/90 via-gray-800/70 to-transparent backdrop-blur-sm p-4">
            <div className="flex items-center justify-center gap-3">
              <motion.div
                className="rounded-xl p-2 bg-white/20 backdrop-blur-sm border border-white/30"
                whileHover={{ scale: 1.1, rotate: 5 }}
                transition={{ duration: 0.2 }}
              >
                {icon}
              </motion.div>
              <h3 className="text-lg font-bold text-white drop-shadow-lg">{title}</h3>
            </div>
          </div>
        </div>
      </Card>
    </motion.div>
  );
}

export function FeaturesSection() {
  const t = useTranslations('features');
  const labels = useTranslations('labels');

  // 功能示例图片
  const featureData = {
    removeWatermark: {
      beforeImage: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop",
      afterImage: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop&sat=-20"
    },
    backgroundEdit: {
      beforeImage: "https://images.unsplash.com/photo-1494790108755-2616c9c0e8e5?w=400&h=300&fit=crop",
      afterImage: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=300&fit=crop"
    },
    objectRemoval: {
      beforeImage: "https://images.unsplash.com/photo-1551963831-b3b1ca40c98e?w=400&h=300&fit=crop",
      afterImage: "https://images.unsplash.com/photo-1551963831-b3b1ca40c98e?w=400&h=300&fit=crop&sat=-30"
    },
    faceRetouch: {
      beforeImage: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=400&h=300&fit=crop",
      afterImage: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=400&h=300&fit=crop&brightness=10"
    },
    styleTransfer: {
      beforeImage: "https://images.unsplash.com/photo-1518717758536-85ae29035b6d?w=400&h=300&fit=crop",
      afterImage: "https://images.unsplash.com/photo-1518717758536-85ae29035b6d?w=400&h=300&fit=crop&sepia=100"
    },
    textReplacement: {
      beforeImage: "https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=400&h=300&fit=crop",
      afterImage: "https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=400&h=300&fit=crop&contrast=20"
    }
  };

  return (
    <section className="py-16">
      <ScrollAnimation direction="up" className="mb-16 text-center">
        <h2 className="mb-4 text-3xl font-bold text-gray-800">
          {t('sectionTitle')}
        </h2>
        <p className="mx-auto max-w-3xl text-xl text-gray-600">
          {t('sectionSubtitle')}
        </p>
      </ScrollAnimation>

      <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
        <ScrollAnimation direction="up" delay={0.1}>
          <FeatureCard
            icon={<Eraser className="h-5 w-5 text-rose-400" />}
            title={t('removeWatermark.title')}
            beforeImage={featureData.removeWatermark.beforeImage}
            afterImage={featureData.removeWatermark.afterImage}
          />
        </ScrollAnimation>

        <ScrollAnimation direction="up" delay={0.2}>
          <FeatureCard
            icon={<Palette className="h-5 w-5 text-orange-400" />}
            title={t('backgroundEdit.title')}
            beforeImage={featureData.backgroundEdit.beforeImage}
            afterImage={featureData.backgroundEdit.afterImage}
          />
        </ScrollAnimation>

        <ScrollAnimation direction="up" delay={0.3}>
          <FeatureCard
            icon={<Scissors className="h-5 w-5 text-amber-500" />}
            title={t('objectRemoval.title')}
            beforeImage={featureData.objectRemoval.beforeImage}
            afterImage={featureData.objectRemoval.afterImage}
          />
        </ScrollAnimation>

        <ScrollAnimation direction="up" delay={0.4}>
          <FeatureCard
            icon={<Sparkles className="h-5 w-5 text-rose-500" />}
            title={t('faceRetouch.title')}
            beforeImage={featureData.faceRetouch.beforeImage}
            afterImage={featureData.faceRetouch.afterImage}
          />
        </ScrollAnimation>

        <ScrollAnimation direction="up" delay={0.5}>
          <FeatureCard
            icon={<Wand2 className="h-5 w-5 text-orange-500" />}
            title={t('styleTransfer.title')}
            beforeImage={featureData.styleTransfer.beforeImage}
            afterImage={featureData.styleTransfer.afterImage}
          />
        </ScrollAnimation>

        <ScrollAnimation direction="up" delay={0.6}>
          <FeatureCard
            icon={<Type className="h-5 w-5 text-amber-600" />}
            title={t('textReplacement.title')}
            beforeImage={featureData.textReplacement.beforeImage}
            afterImage={featureData.textReplacement.afterImage}
          />
        </ScrollAnimation>
      </div>

      {/* CTA Section */}
      <div className="mt-20 text-center">
        <ScrollAnimation direction="up" delay={0.7}>
          <motion.div
            className="relative overflow-hidden rounded-2xl bg-white/80 backdrop-blur-md border border-rose-200/50 p-8 shadow-2xl"
            whileHover={{ scale: 1.02 }}
            transition={{ duration: 0.3 }}
          >
            <div className="absolute inset-0 bg-gradient-to-r from-rose-100/20 via-orange-100/20 to-amber-100/20 animate-pulse" />
            <div className="relative space-y-6">
              <div className="flex items-center justify-center mb-4">
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
                  className="w-16 h-16 bg-gradient-to-r from-rose-500 to-orange-500 rounded-full flex items-center justify-center shadow-lg"
                >
                  <Sparkles className="h-8 w-8 text-white" />
                </motion.div>
              </div>
              <h3 className="text-2xl font-bold text-gray-800">
                {t('ctaTitle')}
              </h3>
              <p className="text-gray-600 max-w-2xl mx-auto text-lg">
                {t('ctaSubtitle')}
              </p>
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Button
                  size="lg"
                  className="bg-gradient-to-r from-rose-500 to-orange-500 hover:from-rose-400 hover:to-orange-400 shadow-lg shadow-rose-500/25 transition-all duration-300 text-white font-semibold px-8 py-3"
                  asChild
                >
                  <Link href="/generate">
                    {t('ctaButton')} <ArrowRight className="ml-2 h-5 w-5" />
                  </Link>
                </Button>
              </motion.div>
            </div>
          </motion.div>
        </ScrollAnimation>
      </div>
    </section>
  );
}
