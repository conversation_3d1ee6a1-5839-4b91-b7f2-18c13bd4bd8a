"use client";

import React, { useState, useEffect, useCallback } from "react";
import Link from "next/link";
import { Menu, X, User, LogIn, LogOut, ChevronDown } from "lucide-react";
import * as motion from "motion/react-client";
import { Button } from "@/components/ui/button";
import { LocaleSwitcher } from "@/components/LocaleSwitcher";
import { cn } from "@/lib/utils";
import { useTranslations } from 'next-intl';
import { useIsClient } from "@/hooks/useIsClient";
import { signIn, signOut } from "next-auth/react";
import Image from "next/image";
import { Session } from "next-auth";

interface NavbarProps {
  session: Session | null;
}

// 用户头像组件
function UserAvatar({ user, size = "sm" }: { user: Session["user"]; size?: "sm" | "md" | "lg" }) {
  const sizeClasses = {
    sm: "h-8 w-8",
    md: "h-10 w-10",
    lg: "h-12 w-12"
  };

  if (user?.image) {
    return (
      <Image
        src={user.image}
        alt={user.name || "用户头像"}
        width={size === "sm" ? 32 : size === "md" ? 40 : 48}
        height={size === "sm" ? 32 : size === "md" ? 40 : 48}
        className={cn("rounded-full object-cover", sizeClasses[size])}
      />
    );
  }

  return (
    <div className={cn(
      "rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-gray-700 font-medium",
      sizeClasses[size]
    )}>
      {user?.name ? user.name.charAt(0).toUpperCase() : <User className="h-4 w-4" />}
    </div>
  );
}

// 用户菜单组件
function UserMenu({ user, onSignOut }: { user: Session["user"]; onSignOut: () => void }) {
  const [isOpen, setIsOpen] = useState(false);
  const t = useTranslations('navbar');

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 p-1 rounded-full hover:bg-gray-100 transition-colors"
      >
        <UserAvatar user={user} size="sm" />
        <ChevronDown className="h-4 w-4 text-gray-600" />
      </button>

      {isOpen && (
        <>
          <div
            className="fixed inset-0 z-40"
            onClick={() => setIsOpen(false)}
          />
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="absolute right-0 top-full mt-2 w-40 bg-white/95 backdrop-blur-md border border-gray-200 rounded-lg shadow-lg z-50"
          >
            <div className="p-2">
              <Link
                href="/profile"
                className="flex items-center gap-2 px-3 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-md transition-colors"
                onClick={() => setIsOpen(false)}
              >
                <User className="h-4 w-4" />
                {t('profile')}
              </Link>
              <button
                onClick={() => {
                  setIsOpen(false);
                  onSignOut();
                }}
                className="flex items-center gap-2 px-3 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-md transition-colors w-full text-left"
              >
                <LogOut className="h-4 w-4" />
                {t('signOut')}
              </button>
            </div>
          </motion.div>
        </>
      )}
    </div>
  );
}

export function Navbar({ session }: NavbarProps) {
  const t = useTranslations('navbar');
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const isClient = useIsClient();

  // 登录处理函数
  const handleSignIn = useCallback(async () => {
    try {
      await signIn('google', {
        callbackUrl: window.location.href,
        redirect: false
      });
    } catch (error) {
      console.error('登录失败:', error);
    }
  }, []);

  // 登出处理函数
  const handleSignOut = useCallback(async () => {
    try {
      await signOut({
        callbackUrl: window.location.href,
        redirect: true
      });
    } catch (error) {
      console.error('登出失败:', error);
    }
  }, []);

  const handleScroll = useCallback(() => {
    if (typeof window !== 'undefined') {
      setIsScrolled(window.scrollY > 150);
    }
  }, []);

  useEffect(() => {
    if (!isClient) return;

    // 检查初始滚动位置
    if(window.scrollY > 150) {
      setIsScrolled(true);
    }

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, [handleScroll, isClient]);

  const closeMenu = useCallback(() => {
    setIsMenuOpen(false);
  }, []);

  useEffect(() => {
    if (!isClient) return;

    if (isMenuOpen) {
      document.body.style.overflow = "hidden";
      document.addEventListener("click", closeMenu);
    } else {
      document.body.style.overflow = "unset";
      document.removeEventListener("click", closeMenu);
    }
    return () => {
      document.body.style.overflow = "unset";
      document.removeEventListener("click", closeMenu);
    };
  }, [isMenuOpen, closeMenu, isClient]);

  // Helper function to check if session exists
  const isLoggedIn = !!session;

  return (
    <nav className="sticky top-0 z-50 flex w-full justify-center">
      <div
        className={cn(
          "transition-all duration-300",
          isScrolled
            ? "mx-auto my-2 w-auto rounded-full bg-white/90 px-4 shadow-lg backdrop-blur-sm border border-rose-200/50"
            : "w-full",
        )}
      >
        <div
          className={cn(
            "px-4 py-4 sm:px-6 lg:px-8",
            isScrolled ? "max-w-fit" : "w-full",
          )}
        >
          <div
            className={cn(
              "flex items-center justify-between gap-8",
              isScrolled ? "justify-center" : "",
            )}
          >
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
              className={cn(
                "flex flex-1 items-center gap-2",
                isScrolled ? "hidden" : "",
              )}
            >
              <Link href="/" className="text-2xl font-bold text-gray-800">Flux Pix</Link>
            </motion.div>

            <div className={cn("md:hidden", isScrolled ? "hidden" : "")}>
              <Button
                variant="ghost"
                size="icon"
                className="text-gray-700"
                onClick={(e) => {
                  e.stopPropagation();
                  setIsMenuOpen(!isMenuOpen);
                }}
                aria-label={isMenuOpen ? t('closeMenu') : t('openMenu')}
              >
                {isMenuOpen ? (
                  <X className="h-6 w-6" />
                ) : (
                  <Menu className="h-6 w-6" />
                )}
              </Button>

              {isMenuOpen && (
                <>
                  <div
                    className="fixed inset-0 z-40 bg-black/60 backdrop-blur-sm"
                    onClick={closeMenu}
                  />
                  <motion.div
                    initial={{ opacity: 0, y: -20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ type: "spring", stiffness: 300, damping: 30 }}
                    className="fixed inset-0 z-50 flex items-start justify-center bg-white/95 pt-16 shadow-lg backdrop-blur-sm"
                    onClick={(e: React.MouseEvent) => e.stopPropagation()}
                  >
                    <div className="mx-4 flex w-full max-w-lg flex-col space-y-6">
                      <div className="flex items-center justify-between">
                        <Link href="/" className="text-2xl font-bold text-gray-800">
                          Flux Pix
                        </Link>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="text-gray-700"
                          onClick={closeMenu}
                          aria-label={t('closeMenu')}
                        >
                          <X className="h-6 w-6" />
                        </Button>
                      </div>
                      <div className="flex flex-col space-y-4">
                        <Link
                          href="/generate"
                          className="flex items-center gap-2 py-2 text-gray-700/80 transition-colors hover:text-gray-700"
                        >
                          {t('generate')}
                        </Link>
                        <Link
                          href="/gallery"
                          className="flex items-center gap-2 py-2 text-gray-700/80 transition-colors hover:text-gray-700"
                        >
                          {t('gallery')}
                        </Link>
                        <Link
                          href="/pricing"
                          className="flex items-center gap-2 py-2 text-gray-700/80 transition-colors hover:text-gray-700"
                        >
                          {t('pricing')}
                        </Link>
                        <div className="border-t border-gray-200 pt-4">
                          <div className="flex flex-col space-y-4">
                            <div className="relative z-[100]">
                              <LocaleSwitcher />
                            </div>
                            {isLoggedIn ? (
                              <div className="flex items-center justify-center gap-3 p-3 bg-gray-50 rounded-lg border border-gray-200">
                                <UserAvatar user={session?.user} size="md" />
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={handleSignOut}
                                  className="border-red-400/50 bg-red-50 text-red-600 hover:bg-red-100"
                                >
                                  <LogOut className="h-4 w-4" />
                                </Button>
                              </div>
                            ) : (
                              <Button
                                variant="outline"
                                className="flex w-full items-center gap-2 border-rose-400/50 bg-gradient-to-r from-rose-500 to-orange-500 text-white shadow-lg shadow-rose-500/20 hover:from-rose-400 hover:to-orange-400"
                                onClick={handleSignIn}
                              >
                                <LogIn className="h-5 w-5" />
                                {t('signInWithGoogle')}
                              </Button>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                </>
              )}
            </div>

            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className={cn(
                "hidden items-center space-x-8 md:flex",
                isScrolled ? "mx-auto md:flex" : "",
              )}
            >
              <Link
                href="/generate"
                className="flex items-center gap-1 text-gray-700/80 transition-colors hover:text-gray-700"
              >
                {t('generate')}
              </Link>
              <Link
                href="/gallery"
                className="text-gray-700/80 transition-colors hover:text-gray-700"
              >
                {t('gallery')}
              </Link>
              <Link
                href="/pricing"
                className="text-gray-700/80 transition-colors hover:text-gray-700"
              >
                {t('pricing')}
              </Link>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
              className={cn(
                "hidden flex-1 items-center justify-end gap-4 md:flex",
                isScrolled ? "!hidden" : "",
              )}
            >
              <LocaleSwitcher />
              {isLoggedIn ? (
                <UserMenu user={session?.user} onSignOut={handleSignOut} />
              ) : (
                <Button
                  variant="outline"
                  className="flex items-center gap-2 border-rose-400/50 bg-gradient-to-r from-rose-500 to-orange-500 text-white shadow-lg shadow-rose-500/20 hover:from-rose-400 hover:to-orange-400"
                  onClick={handleSignIn}
                >
                  <LogIn className="h-4 w-4" />
                  {t('signInWithGoogle')}
                </Button>
              )}
            </motion.div>
          </div>
        </div>
      </div>
    </nav>
  );
}
