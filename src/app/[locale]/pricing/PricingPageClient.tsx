"use client";

import { <PERSON>, <PERSON>, <PERSON>, Zap, Shield, Headphones } from "lucide-react";
import * as motion from "motion/react-client";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { useTranslations } from 'next-intl';
import { Navbar } from "../components/Navbar";
import { Footer } from "../components/Footer";
import { BackgroundBlurs } from "../components/BackgroundBlurs";
import { Session } from "next-auth";

interface PricingFeature {
  text: string;
  included: boolean;
}

interface PricingPlanProps {
  title: string;
  price: string;
  description: string;
  features: PricingFeature[];
  isPopular?: boolean;
  buttonText: string;
  credits: string;
  popularLabel: string;
}

function PricingPlan({
  title,
  price,
  description,
  features,
  isPopular = false,
  buttonText,
  credits,
  popularLabel,
}: PricingPlanProps) {
  return (
    <motion.div
      whileHover={{ scale: 1.02 }}
      transition={{ duration: 0.2 }}
      className="relative h-full"
    >
      {isPopular && (
        <div className="absolute -top-3 left-1/2 -translate-x-1/2 rounded-full bg-gradient-to-r from-rose-500 to-orange-500 px-4 py-2 text-sm font-medium text-white z-10 shadow-lg">
          <Star className="inline h-4 w-4 mr-1" />
          {popularLabel}
        </div>
      )}

      <Card className={`border-none backdrop-blur-sm h-full transition-all duration-300 ${
        isPopular
          ? "bg-white/90 border-2 border-rose-300/50 shadow-xl shadow-rose-200/50 scale-105"
          : "bg-white/80 border border-gray-200/50 shadow-lg"
      }`}>
        <CardContent className="p-8 space-y-6 h-full flex flex-col">
          {/* Header */}
          <div className="text-center">
            <h3 className="mb-3 text-2xl font-bold text-gray-800">{title}</h3>
            <div className="mb-3">
              <span className="text-4xl font-bold text-gray-800">{price}</span>
              {price !== "¥0" && <span className="text-gray-600 ml-1">/月</span>}
            </div>
            <p className="text-gray-600">{description}</p>
          </div>

          {/* Credits - Enhanced Design */}
          <div className={`rounded-xl p-4 text-center border ${
            isPopular 
              ? "bg-gradient-to-r from-rose-50 to-orange-50 border-rose-200/50" 
              : "bg-gray-50 border-gray-200/50"
          }`}>
            <div className="flex items-center justify-center gap-2 mb-2">
              <Zap className={`h-5 w-5 ${isPopular ? "text-rose-500" : "text-gray-600"}`} />
              <span className="font-semibold text-gray-800">{credits}</span>
            </div>
            {isPopular && (
              <p className="text-sm text-gray-600">最受欢迎的选择</p>
            )}
          </div>

          {/* Features */}
          <div className="flex-1">
            <ul className="space-y-4">
              {features.map((feature, index) => (
                <li key={index} className="flex items-start gap-3">
                  {feature.included ? (
                    <div className="rounded-full bg-green-100 p-1 mt-0.5">
                      <Check className="h-4 w-4 text-green-600" />
                    </div>
                  ) : (
                    <div className="rounded-full bg-gray-100 p-1 mt-0.5">
                      <X className="h-4 w-4 text-gray-400" />
                    </div>
                  )}
                  <span
                    className={`text-sm leading-relaxed ${
                      feature.included ? "text-gray-700" : "text-gray-500"
                    }`}
                  >
                    {feature.text}
                  </span>
                </li>
              ))}
            </ul>
          </div>

          {/* Button */}
          <motion.div
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <Button
              size="lg"
              className={`w-full py-3 font-semibold transition-all duration-300 ${
                isPopular
                  ? "bg-gradient-to-r from-rose-500 to-orange-500 hover:from-rose-400 hover:to-orange-400 text-white shadow-lg shadow-rose-500/25"
                  : "bg-gray-800 hover:bg-gray-700 text-white"
              }`}
            >
              {buttonText}
            </Button>
          </motion.div>
        </CardContent>
      </Card>
    </motion.div>
  );
}

interface PricingPageClientProps {
  session: Session | null;
}

export default function PricingPageClient({ session }: PricingPageClientProps) {
  const t = useTranslations('pricing');
  
  const freePlanFeatures = [
    { text: t('freePlan.features.dailyCredits'), included: true },
    { text: t('freePlan.features.trialPeriod'), included: true },
    { text: t('freePlan.features.resultStorage'), included: true },
    { text: t('freePlan.features.privateResults'), included: false },
    { text: t('freePlan.features.commercialUse'), included: false },
    { text: t('freePlan.features.advertisements'), included: false },
  ];

  const basicPlanFeatures = [
    { text: t('basicPlan.features.monthlyCredits'), included: true },
    { text: t('basicPlan.features.noAds'), included: true },
    { text: t('basicPlan.features.longTermStorage'), included: true },
    { text: t('basicPlan.features.privateResults'), included: true },
    { text: t('basicPlan.features.commercialUse'), included: true },
    { text: t('basicPlan.features.unlimitedPrompts'), included: false },
  ];

  const proPlanFeatures = [
    { text: t('proPlan.features.monthlyCredits'), included: true },
    { text: t('proPlan.features.unlimitedPrompts'), included: true },
    { text: t('proPlan.features.noAds'), included: true },
    { text: t('proPlan.features.longTermStorage'), included: true },
    { text: t('proPlan.features.privateResults'), included: true },
    { text: t('proPlan.features.commercialUse'), included: true },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-rose-50 via-orange-50 to-amber-50">
      <BackgroundBlurs />
      <Navbar session={session} />
      
      <div className="relative z-10 container mx-auto px-4 py-16">
        {/* Hero Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-r from-rose-500/10 to-orange-500/10 rounded-2xl blur-xl" />
            <div className="relative bg-white/80 backdrop-blur-md border border-rose-200/50 rounded-2xl p-8 shadow-lg">
              <h1 className="text-4xl md:text-5xl font-bold text-gray-800 mb-4">
                {t('sectionTitle')}
              </h1>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                {t('sectionSubtitle')}
              </p>
            </div>
          </div>
        </motion.div>

        {/* Pricing Plans */}
        <div className="grid grid-cols-1 gap-8 md:grid-cols-3 mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <PricingPlan
              title={t('freePlan.title')}
              price={t('freePlan.price')}
              description={t('freePlan.description')}
              features={freePlanFeatures}
              buttonText={t('freePlan.buttonText')}
              credits={t('freePlan.credits')}
              popularLabel={t('popularLabel')}
            />
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <PricingPlan
              title={t('basicPlan.title')}
              price={t('basicPlan.price')}
              description={t('basicPlan.description')}
              features={basicPlanFeatures}
              isPopular={true}
              buttonText={t('basicPlan.buttonText')}
              credits={t('basicPlan.credits')}
              popularLabel={t('popularLabel')}
            />
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <PricingPlan
              title={t('proPlan.title')}
              price={t('proPlan.price')}
              description={t('proPlan.description')}
              features={proPlanFeatures}
              buttonText={t('proPlan.buttonText')}
              credits={t('proPlan.credits')}
              popularLabel={t('popularLabel')}
            />
          </motion.div>
        </div>

        {/* FAQ or Additional Info Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.5 }}
          className="text-center"
        >
          <div className="bg-white/80 backdrop-blur-md border border-rose-200/50 rounded-2xl p-8 shadow-lg">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="flex flex-col items-center text-center">
                <div className="w-12 h-12 bg-gradient-to-r from-rose-500 to-orange-500 rounded-full flex items-center justify-center mb-4">
                  <Shield className="h-6 w-6 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-gray-800 mb-2">安全可靠</h3>
                <p className="text-gray-600 text-sm">所有数据传输均采用SSL加密，确保您的图片安全</p>
              </div>
              
              <div className="flex flex-col items-center text-center">
                <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-amber-500 rounded-full flex items-center justify-center mb-4">
                  <Zap className="h-6 w-6 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-gray-800 mb-2">快速处理</h3>
                <p className="text-gray-600 text-sm">AI算法优化，平均处理时间仅需10-30秒</p>
              </div>
              
              <div className="flex flex-col items-center text-center">
                <div className="w-12 h-12 bg-gradient-to-r from-amber-500 to-rose-500 rounded-full flex items-center justify-center mb-4">
                  <Headphones className="h-6 w-6 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-gray-800 mb-2">专业支持</h3>
                <p className="text-gray-600 text-sm">7x24小时客服支持，随时为您解答问题</p>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
      
      <Footer />
    </div>
  );
}