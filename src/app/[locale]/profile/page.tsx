"use client";

import React from "react";
import { useSession } from "next-auth/react";
import { redirect } from "next/navigation";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { User, Mail, Calendar, LogOut, Coins, Crown, Clock } from "lucide-react";
import { api } from "@/trpc/react";
import { signOut } from "next-auth/react";
import Image from "next/image";
import { useTranslations } from 'next-intl';
import { cn } from "@/lib/utils";

export default function ProfilePage() {
  const { data: session, status } = useSession();
  const t = useTranslations('profile');
  const creditsT = useTranslations('credits');

  // 获取用户积分信息
  const { data: userWithCredits } = api.credits.getUserWithCredits.useQuery(
    undefined,
    { enabled: !!session }
  );

  // 获取积分历史
  const { data: creditHistory } = api.credits.getCreditHistory.useQuery(
    { limit: 10 },
    { enabled: !!session }
  );

  if (status === "loading") {
    return (
      <div className="min-h-screen bg-gradient-to-br from-rose-50 via-orange-50 to-amber-50 flex items-center justify-center">
        <div className="text-rose-600">{t('loading')}</div>
      </div>
    );
  }

  if (!session) {
    redirect("/");
  }

  const handleSignOut = async () => {
    try {
      await signOut({
        callbackUrl: "/",
        redirect: true
      });
    } catch (error) {
      console.error('登出失败:', error);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-rose-50 via-orange-50 to-amber-50">
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-2xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-rose-800 mb-2">{t('title')}</h1>
            <p className="text-rose-600">{t('subtitle')}</p>
          </div>

          <Card className="bg-white/80 backdrop-blur-md border-rose-200/50 p-8 shadow-lg">
            <div className="flex flex-col items-center space-y-6">
              <div className="relative">
                {session.user?.image ? (
                  <Image
                    src={session.user.image}
                    alt={session.user.name || "用户头像"}
                    width={120}
                    height={120}
                    className="rounded-full object-cover"
                  />
                ) : (
                  <div className="w-30 h-30 rounded-full bg-gradient-to-r from-rose-400 to-orange-400 flex items-center justify-center">
                    <User className="w-16 h-16 text-white" />
                  </div>
                )}
              </div>

              <div className="text-center space-y-2">
                <h2 className="text-2xl font-bold text-rose-800">
                  {session.user?.name || t('anonymousUser')}
                </h2>
                <p className="text-rose-600">{t('welcome')}</p>
              </div>

              <div className="w-full space-y-4">
                <div className="flex items-center space-x-4 p-4 bg-rose-50/50 rounded-lg border border-rose-100">
                  <Mail className="w-5 h-5 text-rose-500" />
                  <div>
                    <p className="text-sm text-rose-500">{t('email')}</p>
                    <p className="text-rose-800">{session.user?.email}</p>
                  </div>
                </div>

                <div className="flex items-center space-x-4 p-4 bg-rose-50/50 rounded-lg border border-rose-100">
                  <User className="w-5 h-5 text-rose-500" />
                  <div>
                    <p className="text-sm text-rose-500">{t('name')}</p>
                    <p className="text-rose-800">{session.user?.name || t('notSet')}</p>
                  </div>
                </div>

                <div className="flex items-center space-x-4 p-4 bg-rose-50/50 rounded-lg border border-rose-100">
                  <Calendar className="w-5 h-5 text-rose-500" />
                  <div>
                    <p className="text-sm text-rose-500">{t('joinedDate')}</p>
                    <p className="text-rose-800">{new Date().toLocaleDateString('zh-CN')}</p>
                  </div>
                </div>

                {/* 用户类型 */}
                {userWithCredits && (
                  <div className="flex items-center space-x-4 p-4 bg-amber-50/50 rounded-lg border border-amber-100">
                    <Crown className="w-5 h-5 text-amber-500" />
                    <div>
                      <p className="text-sm text-amber-500">{creditsT('userType.title')}</p>
                      <p className="text-amber-800">
                        {creditsT(`userType.${userWithCredits.userType}`)}
                      </p>
                    </div>
                  </div>
                )}

                {/* 积分信息 */}
                {userWithCredits?.credits && (
                  <div className="flex items-center space-x-4 p-4 bg-green-50/50 rounded-lg border border-green-100">
                    <Coins className="w-5 h-5 text-green-500" />
                    <div className="flex-1">
                      <p className="text-sm text-green-500">{creditsT('currentCredits')}</p>
                      <div className="flex items-center justify-between">
                        <p className="text-green-800 text-lg font-semibold">
                          {userWithCredits.credits.availableCredits} / {userWithCredits.credits.totalCredits}
                        </p>
                        <p className="text-xs text-green-600">
                          已使用 {userWithCredits.credits.usedCredits}
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {/* 每日积分刷新信息（仅免费用户） */}
                {userWithCredits?.userType === 'free' && userWithCredits?.credits && (
                  <div className="flex items-center space-x-4 p-4 bg-blue-50/50 rounded-lg border border-blue-100">
                    <Clock className="w-5 h-5 text-blue-500" />
                    <div>
                      <p className="text-sm text-blue-500">每日积分</p>
                      <p className="text-blue-800">
                        上次刷新: {new Date(userWithCredits.credits.lastDailyRefresh).toLocaleDateString('zh-CN')}
                      </p>
                      <p className="text-xs text-blue-600">
                        每天可获得 {userWithCredits.credits.dailyCredits} 积分
                      </p>
                    </div>
                  </div>
                )}
              </div>

              <div className="w-full pt-4">
                <Button
                  variant="outline"
                  className="w-full flex items-center gap-2 border-red-400/50 bg-red-50 text-red-600 hover:bg-red-100"
                  onClick={handleSignOut}
                >
                  <LogOut className="w-4 h-4" />
                  {t('signOut')}
                </Button>
              </div>
            </div>
          </Card>

          {/* 积分历史 */}
          {creditHistory && creditHistory.length > 0 && (
            <Card className="bg-white/80 backdrop-blur-md border-rose-200/50 p-8 shadow-lg mt-6">
              <h3 className="text-xl font-bold text-rose-800 mb-6 flex items-center gap-2">
                <Coins className="w-5 h-5" />
                积分历史
              </h3>
              <div className="space-y-3">
                {creditHistory.map((transaction) => (
                  <div
                    key={transaction.id}
                    className="flex items-center justify-between p-4 bg-gray-50/50 rounded-lg border border-gray-100"
                  >
                    <div className="flex items-center gap-3">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                        transaction.amount > 0
                          ? 'bg-green-100 text-green-600'
                          : 'bg-red-100 text-red-600'
                      }`}>
                        <Coins className="w-4 h-4" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-800">
                          {transaction.description}
                        </p>
                        <p className="text-xs text-gray-500">
                          {new Date(transaction.createdAt).toLocaleString('zh-CN')}
                        </p>
                      </div>
                    </div>
                    <div className={`text-right ${
                      transaction.amount > 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      <p className="font-semibold">
                        {transaction.amount > 0 ? '+' : ''}{transaction.amount}
                      </p>
                      <p className="text-xs text-gray-500">
                        {transaction.type}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}