import { relations, sql } from "drizzle-orm";
import {
  index,
  integer,
  jsonb,
  numeric,
  pgTableCreator,
  primaryKey,
  text,
  timestamp,
  varchar,
} from "drizzle-orm/pg-core";
import { type AdapterAccount } from "next-auth/adapters";

/**
 * This is an example of how to use the multi-project schema feature of Drizzle ORM. Use the same
 * database instance for multiple projects.
 *
 * @see https://orm.drizzle.team/docs/goodies#multi-project-schema
 */
export const createTable = pgTableCreator((name) => `app-template_${name}`);

export const users = createTable("user", {
  id: varchar("id", { length: 255 })
    .notNull()
    .primaryKey()
    .$defaultFn(() => crypto.randomUUID()),
  name: varchar("name", { length: 255 }),
  email: varchar("email", { length: 255 }).notNull(),
  emailVerified: timestamp("email_verified", {
    mode: "date",
    withTimezone: true,
  }).default(sql`CURRENT_TIMESTAMP`),
  image: varchar("image", { length: 255 }),
  userType: varchar("user_type", { length: 20 }).notNull().default("free"), // 'free' | 'premium' | 'pro'
  createdAt: timestamp("created_at", {
    mode: "date",
    withTimezone: true,
  }).default(sql`CURRENT_TIMESTAMP`).notNull(),
});

export const userCredits = createTable("user_credits", {
  id: varchar("id", { length: 255 })
    .notNull()
    .primaryKey()
    .$defaultFn(() => crypto.randomUUID()),
  userId: varchar("user_id", { length: 255 })
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  totalCredits: integer("total_credits").notNull().default(0),
  usedCredits: integer("used_credits").notNull().default(0),
  dailyCredits: integer("daily_credits").notNull().default(10),
  lastDailyRefresh: timestamp("last_daily_refresh", {
    mode: "date",
    withTimezone: true,
  }).default(sql`CURRENT_TIMESTAMP`).notNull(),
  createdAt: timestamp("created_at", {
    mode: "date",
    withTimezone: true,
  }).default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at", {
    mode: "date",
    withTimezone: true,
  }).default(sql`CURRENT_TIMESTAMP`).notNull(),
}, (table) => ({
  userIdIdx: index("user_credits_user_id_idx").on(table.userId),
}));

export const usersRelations = relations(users, ({ many, one }) => ({
  accounts: many(accounts),
  credits: one(userCredits, { fields: [users.id], references: [userCredits.userId] }),
  paymentIntents: many(paymentIntents),
}));

export const userCreditsRelations = relations(userCredits, ({ one, many }) => ({
  user: one(users, { fields: [userCredits.userId], references: [users.id] }),
  transactions: many(creditTransactions),
}));

export const creditTransactions = createTable("credit_transactions", {
  id: varchar("id", { length: 255 })
    .notNull()
    .primaryKey()
    .$defaultFn(() => crypto.randomUUID()),
  userId: varchar("user_id", { length: 255 })
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  type: varchar("type", { length: 50 }).notNull(), // 'daily_grant', 'usage', 'purchase', 'bonus'
  amount: integer("amount").notNull(), // 正数为获得，负数为消耗
  description: text("description"),
  relatedId: varchar("related_id", { length: 255 }), // 关联的创作ID或其他相关ID
  createdAt: timestamp("created_at", {
    mode: "date",
    withTimezone: true,
  }).default(sql`CURRENT_TIMESTAMP`).notNull(),
}, (table) => ({
  userIdIdx: index("credit_transactions_user_id_idx").on(table.userId),
  typeIdx: index("credit_transactions_type_idx").on(table.type),
  createdAtIdx: index("credit_transactions_created_at_idx").on(table.createdAt),
}));

export const creditTransactionsRelations = relations(creditTransactions, ({ one }) => ({
  user: one(users, { fields: [creditTransactions.userId], references: [users.id] }),
}));

export const accounts = createTable(
  "account",
  {
    userId: varchar("user_id", { length: 255 })
      .notNull()
      .references(() => users.id),
    type: varchar("type", { length: 255 })
      .$type<AdapterAccount["type"]>()
      .notNull(),
    provider: varchar("provider", { length: 255 }).notNull(),
    providerAccountId: varchar("provider_account_id", {
      length: 255,
    }).notNull(),
    refresh_token: text("refresh_token"),
    access_token: text("access_token"),
    expires_at: integer("expires_at"),
    token_type: varchar("token_type", { length: 255 }),
    scope: varchar("scope", { length: 255 }),
    id_token: text("id_token"),
    session_state: varchar("session_state", { length: 255 }),
  },
  (account) => ({
    compoundKey: primaryKey({
      columns: [account.provider, account.providerAccountId],
    }),
    userIdIdx: index("account_user_id_idx").on(account.userId),
  })
);

export const accountsRelations = relations(accounts, ({ one }) => ({
  user: one(users, { fields: [accounts.userId], references: [users.id] }),
}));

export const sessions = createTable(
  "session",
  {
    sessionToken: varchar("session_token", { length: 255 })
      .notNull()
      .primaryKey(),
    userId: varchar("user_id", { length: 255 })
      .notNull()
      .references(() => users.id),
    expires: timestamp("expires", {
      mode: "date",
      withTimezone: true,
    }).notNull(),
  },
  (session) => ({
    userIdIdx: index("session_user_id_idx").on(session.userId),
  })
);

export const sessionsRelations = relations(sessions, ({ one }) => ({
  user: one(users, { fields: [sessions.userId], references: [users.id] }),
}));

export const verificationTokens = createTable(
  "verification_token",
  {
    identifier: varchar("identifier", { length: 255 }).notNull(),
    token: varchar("token", { length: 255 }).notNull(),
    expires: timestamp("expires", {
      mode: "date",
      withTimezone: true,
    }).notNull(),
  },
  (vt) => ({
    compoundKey: primaryKey({ columns: [vt.identifier, vt.token] }),
  })
);

export const subscriptionPlans = createTable(
  "subscription_plan",
  {
    id: varchar("id", { length: 255 })
    .notNull()
    .primaryKey()
    .$defaultFn(() => crypto.randomUUID()),
    planName: varchar("plan_name", { length: 50 }).notNull().unique(),
    priceMonthly: numeric("price_monthly", { precision: 10, scale: 2 }).notNull(),
    creditsPerMonth: integer("credits_per_month").notNull(),
    features: jsonb("features"),
  }
);
export const assets = createTable(
  "asset",
  {
    id: varchar("id", { length: 255 })
    .notNull()
    .primaryKey()
    .$defaultFn(() => crypto.randomUUID()),
    userId: varchar("user-id", { length: 255 })
    .notNull()
    .references(() => users.id),
    url: text("url").notNull().unique(),
    createdAt: timestamp("created_at", { withTimezone: true })
      .default(sql`CURRENT_TIMESTAMP`)
      .notNull(),
  }
);

export const creations = createTable(
  "creation",
  {
    id: varchar("id", { length: 255 })
    .notNull()
    .primaryKey()
    .$defaultFn(() => crypto.randomUUID()),
    userId: varchar("user-id", { length: 255 })
      .notNull()
      .references(() => users.id, { onDelete: "cascade" }),
    prompt: text("prompt"),
    originalAssetId: varchar("user-id", { length: 255 })
      .notNull()
      .references(() => assets.id),
    resultAssetId: varchar("user-id", { length: 255 })
      .references(() => assets.id),
    aiModel: varchar("ai_model", { length: 100 }),
    creditsConsumed: integer("credits_consumed"),
    createdAt: timestamp("created_at", { withTimezone: true })
      .default(sql`CURRENT_TIMESTAMP`)
      .notNull(),
  }
);

export const paymentIntents = createTable(
  "payment_intent",
  {
    id: varchar("id", { length: 255 })
      .notNull()
      .primaryKey()
      .$defaultFn(() => crypto.randomUUID()),
    userId: varchar("user_id", { length: 255 })
      .notNull()
      .references(() => users.id, { onDelete: "cascade" }), // 有付费意愿的用户ID
    createdAt: timestamp("created_at", {
      mode: "date",
      withTimezone: true,
    }).default(sql`CURRENT_TIMESTAMP`).notNull(), // 记录时间
  },
  (table) => ({
    userIdIdx: index("payment_intent_user_id_idx").on(table.userId),
    createdAtIdx: index("payment_intent_created_at_idx").on(table.createdAt),
  })
);

// 添加关系
export const assetsRelations = relations(assets, ({ one }) => ({
  uploader: one(users, { fields: [assets.userId], references: [users.id] }),
}));

export const creationsRelations = relations(creations, ({ one }) => ({
  user: one(users, { fields: [creations.userId], references: [users.id] }),
  originalAsset: one(assets, { fields: [creations.originalAssetId], references: [assets.id] }),
  resultAsset: one(assets, { fields: [creations.resultAssetId], references: [assets.id] }),
}));

export const paymentIntentsRelations = relations(paymentIntents, ({ one }) => ({
  user: one(users, { fields: [paymentIntents.userId], references: [users.id] }),
}));