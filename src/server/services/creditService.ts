import { db } from "@/server/db";
import { userCredits, creditTransactions, users } from "@/server/db/schema";
import { eq, and, gte, lt, sql } from "drizzle-orm";
import {
  getCreditConfig,
  getDailyCredits,
  getNewUserBonus,
  calculateDailyCreditsToGrant,
  canReceiveDailyCredits
} from "@/lib/credits-config";

export class CreditService {
  /**
   * 为新用户初始化积分账户
   */
  static async initializeUserCredits(userId: string, userType: 'free' | 'premium' | 'pro' = 'free'): Promise<void> {
    const now = new Date();

    // 检查用户是否已有积分记录
    const existingCredits = await db
      .select()
      .from(userCredits)
      .where(eq(userCredits.userId, userId))
      .limit(1);

    if (existingCredits.length > 0) {
      return; // 用户已有积分记录，不需要重复初始化
    }

    // 获取用户类型的积分配置
    const newUserBonus = getNewUserBonus(userType);
    const dailyCredits = getDailyCredits(userType);

    // 创建积分记录
    await db.insert(userCredits).values({
      userId,
      totalCredits: newUserBonus,
      usedCredits: 0,
      dailyCredits: dailyCredits,
      lastDailyRefresh: now,
    });

    // 记录积分获得历史
    await db.insert(creditTransactions).values({
      userId,
      type: 'daily_grant',
      amount: newUserBonus,
      description: `新用户奖励积分 (${userType})`,
      createdAt: now,
    });
  }

  /**
   * 检查并刷新用户的每日积分（仅免费用户）
   */
  static async refreshDailyCredits(userId: string): Promise<boolean> {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    // 先获取用户类型
    const user = await db
      .select({ userType: users.userType })
      .from(users)
      .where(eq(users.id, userId))
      .limit(1);

    if (user.length === 0) {
      return false; // 用户不存在
    }

    const userType = (user[0]?.userType ?? 'free') as 'free' | 'premium' | 'pro';

    // 获取用户积分信息
    const userCredit = await db
      .select()
      .from(userCredits)
      .where(eq(userCredits.userId, userId))
      .limit(1);

    if (userCredit.length === 0) {
      // 如果用户没有积分记录，先初始化
      await this.initializeUserCredits(userId, userType);
      return true;
    }

    const credit = userCredit[0]!;
    const lastRefresh = new Date(credit.lastDailyRefresh);
    const lastRefreshDate = new Date(lastRefresh.getFullYear(), lastRefresh.getMonth(), lastRefresh.getDate());

    // 检查是否需要刷新（今天还没有刷新过）
    if (today > lastRefreshDate) {
      // 计算应该赠送的积分数量
      const creditsToGrant = calculateDailyCreditsToGrant(userType, credit.totalCredits - credit.usedCredits);

      if (creditsToGrant > 0) {
        // 更新积分
        await db
          .update(userCredits)
          .set({
            totalCredits: credit.totalCredits + creditsToGrant,
            lastDailyRefresh: now,
            updatedAt: now,
          })
          .where(eq(userCredits.userId, userId));

        // 记录积分获得历史
        await db.insert(creditTransactions).values({
          userId,
          type: 'daily_grant',
          amount: creditsToGrant,
          description: `每日积分刷新 (${userType})`,
          createdAt: now,
        });

        return true;
      } else {
        // 即使不赠送积分，也要更新刷新时间
        await db
          .update(userCredits)
          .set({
            lastDailyRefresh: now,
            updatedAt: now,
          })
          .where(eq(userCredits.userId, userId));
      }
    }

    return false;
  }

  /**
   * 获取用户当前积分信息（自动刷新每日积分）
   */
  static async getUserCredits(userId: string) {
    // 先尝试刷新每日积分
    await this.refreshDailyCredits(userId);

    const userCredit = await db
      .select()
      .from(userCredits)
      .where(eq(userCredits.userId, userId))
      .limit(1);

    if (userCredit.length === 0) {
      return null;
    }

    const credit = userCredit[0]!;
    return {
      totalCredits: credit.totalCredits,
      usedCredits: credit.usedCredits,
      availableCredits: credit.totalCredits - credit.usedCredits,
      dailyCredits: credit.dailyCredits,
      lastDailyRefresh: credit.lastDailyRefresh,
    };
  }

  /**
   * 消耗用户积分（自动刷新每日积分）
   */
  static async consumeCredits(
    userId: string,
    amount: number,
    description: string,
    relatedId?: string
  ): Promise<boolean> {
    // 先刷新每日积分
    await this.refreshDailyCredits(userId);

    // 获取当前积分
    const userCredit = await db
      .select()
      .from(userCredits)
      .where(eq(userCredits.userId, userId))
      .limit(1);

    if (userCredit.length === 0) {
      return false;
    }

    const credit = userCredit[0]!;
    const availableCredits = credit.totalCredits - credit.usedCredits;

    if (availableCredits < amount) {
      return false; // 积分不足
    }

    const now = new Date();

    // 更新已使用积分
    await db
      .update(userCredits)
      .set({
        usedCredits: credit.usedCredits + amount,
        updatedAt: now,
      })
      .where(eq(userCredits.userId, userId));

    // 记录积分消耗历史
    await db.insert(creditTransactions).values({
      userId,
      type: 'usage',
      amount: -amount,
      description,
      relatedId,
      createdAt: now,
    });

    return true;
  }

  /**
   * 获取用户积分交易历史
   */
  static async getCreditHistory(userId: string, limit = 50) {
    return await db
      .select()
      .from(creditTransactions)
      .where(eq(creditTransactions.userId, userId))
      .orderBy(sql`${creditTransactions.createdAt} DESC`)
      .limit(limit);
  }

  /**
   * 检查用户是否有足够积分（自动刷新每日积分）
   */
  static async hasEnoughCredits(userId: string, amount: number): Promise<boolean> {
    const credits = await this.getUserCredits(userId);
    return credits ? credits.availableCredits >= amount : false;
  }

  /**
   * 获取用户类型和积分信息
   */
  static async getUserWithCredits(userId: string) {
    // 先刷新每日积分
    await this.refreshDailyCredits(userId);

    const result = await db
      .select({
        userType: users.userType,
        credits: userCredits,
      })
      .from(users)
      .leftJoin(userCredits, eq(users.id, userCredits.userId))
      .where(eq(users.id, userId))
      .limit(1);

    if (result.length === 0) {
      return null;
    }

    const data = result[0]!;
    const { userType, credits } = data;

    if (!credits) {
      return {
        userType,
        credits: null,
      };
    }

    return {
      userType,
      credits: {
        totalCredits: credits.totalCredits,
        usedCredits: credits.usedCredits,
        availableCredits: credits.totalCredits - credits.usedCredits,
        dailyCredits: credits.dailyCredits,
        lastDailyRefresh: credits.lastDailyRefresh,
      },
    };
  }

  /**
   * 退还用户积分（用于处理失败时）
   */
  static async refundCredits(
    userId: string,
    amount: number,
    description: string,
    relatedId?: string
  ): Promise<boolean> {
    // 获取当前积分
    const userCredit = await db
      .select()
      .from(userCredits)
      .where(eq(userCredits.userId, userId))
      .limit(1);

    if (userCredit.length === 0) {
      return false; // 用户积分记录不存在
    }

    const credit = userCredit[0]!;
    const now = new Date();

    // 减少已使用积分（相当于退还）
    const newUsedCredits = Math.max(0, credit.usedCredits - amount);

    // 更新积分记录
    await db
      .update(userCredits)
      .set({
        usedCredits: newUsedCredits,
        updatedAt: now,
      })
      .where(eq(userCredits.userId, userId));

    // 记录积分退还历史
    await db.insert(creditTransactions).values({
      userId,
      type: 'refund',
      amount: amount, // 正数表示退还
      description,
      relatedId,
      createdAt: now,
    });

    return true;
  }


}
