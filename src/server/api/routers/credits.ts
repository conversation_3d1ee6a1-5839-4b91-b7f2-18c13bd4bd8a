import { z } from "zod";

import {
  createTRPCRouter,
  protectedProcedure,
  publicProcedure,
} from "@/server/api/trpc";
import { CreditService } from "@/server/services/creditService";
import { getAllCreditConfigs, getCreditConfig } from "@/lib/credits-config";

export const creditsRouter = createTRPCRouter({
  /**
   * 获取积分配置信息
   */
  getCreditConfigs: publicProcedure
    .query(async () => {
      return getAllCreditConfigs().map(config => ({
        userType: config.userType,
        dailyCredits: config.dailyCredits,
        maxDailyCredits: config.maxDailyCredits,
        newUserBonus: config.newUserBonus,
        features: config.features,
      }));
    }),

  /**
   * 获取指定用户类型的积分配置
   */
  getCreditConfig: publicProcedure
    .input(z.object({
      userType: z.enum(['free', 'premium', 'pro'])
    }))
    .query(async ({ input }) => {
      const config = getCreditConfig(input.userType);
      return {
        userType: config.userType,
        dailyCredits: config.dailyCredits,
        maxDailyCredits: config.maxDailyCredits,
        newUserBonus: config.newUserBonus,
        features: config.features,
      };
    }),

  /**
   * 获取用户当前积分信息
   */
  getUserCredits: protectedProcedure.query(async ({ ctx }) => {
    const userId = ctx.session.user.id;
    const credits = await CreditService.getUserCredits(userId);

    if (!credits) {
      // 如果用户没有积分记录，初始化一个
      await CreditService.initializeUserCredits(userId);
      return await CreditService.getUserCredits(userId);
    }

    return credits;
  }),

  /**
   * 获取用户类型和积分信息
   */
  getUserWithCredits: protectedProcedure.query(async ({ ctx }) => {
    const userId = ctx.session.user.id;
    const result = await CreditService.getUserWithCredits(userId);

    if (!result) {
      // 如果用户不存在，返回null
      return null;
    }

    if (!result.credits) {
      // 如果用户没有积分记录，初始化一个
      await CreditService.initializeUserCredits(userId);
      return await CreditService.getUserWithCredits(userId);
    }

    return result;
  }),

  /**
   * 获取用户积分交易历史
   */
  getCreditHistory: protectedProcedure
    .input(z.object({
      limit: z.number().min(1).max(100).default(20),
    }))
    .query(async ({ ctx, input }) => {
      const userId = ctx.session.user.id;
      return await CreditService.getCreditHistory(userId, input.limit);
    }),

  /**
   * 检查用户是否有足够积分
   */
  checkCredits: protectedProcedure
    .input(z.object({
      amount: z.number().min(1),
    }))
    .query(async ({ ctx, input }) => {
      const userId = ctx.session.user.id;
      return await CreditService.hasEnoughCredits(userId, input.amount);
    }),

  /**
   * 消耗用户积分
   */
  consumeCredits: protectedProcedure
    .input(z.object({
      amount: z.number().min(1),
      description: z.string(),
      relatedId: z.string().optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      const userId = ctx.session.user.id;
      const success = await CreditService.consumeCredits(
        userId,
        input.amount,
        input.description,
        input.relatedId
      );
      
      if (!success) {
        throw new Error("积分不足或消耗失败");
      }
      
      return { success: true };
    }),

  /**
   * 手动刷新每日积分（用于测试）
   */
  refreshDailyCredits: protectedProcedure.mutation(async ({ ctx }) => {
    const userId = ctx.session.user.id;
    const refreshed = await CreditService.refreshDailyCredits(userId);
    
    return { 
      refreshed,
      message: refreshed ? "每日积分已刷新" : "今日已刷新过积分"
    };
  }),
});
