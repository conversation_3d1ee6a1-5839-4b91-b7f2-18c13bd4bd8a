import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "../trpc";
import { paymentIntents } from "../../db/schema";

export const paymentIntentRouter = createTRPCRouter({
  // 收集有付费意愿的用户
  collect: protectedProcedure
    .mutation(async ({ ctx }) => {
      const userId = ctx.session.user.id;

      try {
        // 只记录有付费意愿的用户ID和时间
        const result = await ctx.db.insert(paymentIntents).values({
          userId,
        }).returning();

        const paymentIntent = result[0];

        // 简单日志记录
        console.log('Payment intent collected:', {
          id: paymentIntent?.id,
          userId,
          createdAt: paymentIntent?.createdAt?.toISOString(),
        });

        return {
          success: true,
          message: 'Payment intent recorded successfully',
          id: paymentIntent?.id,
        };

      } catch (error) {
        console.error('Failed to record payment intent:', error);
        throw new Error('Failed to record payment intent');
      }
    }),
});
