import { z } from "zod";

import {
  createTRPCRouter,
  protectedProcedure,
  publicProcedure,
} from "@/server/api/trpc";
import { fal } from "@fal-ai/client";
import { requireCredits, checkCredits } from "@/server/middleware/creditMiddleware";
import { CreditService } from "@/server/services/creditService";
import { uploadImageFromUrl } from "@/lib/r2-upload";
import { TRPCError } from "@trpc/server";
import { getTranslations } from "next-intl/server";
import { getModelConfig, getModelCredits, getDefaultImageEditingModel, isValidModel, getAllModels } from "@/lib/model-config";

export const creationRouter = createTRPCRouter({
  // 获取可用模型列表
  getAvailableModels: publicProcedure
    .input(z.object({
      category: z.enum(['image-editing', 'text-to-image', 'image-to-image']).optional()
    }))
    .query(async ({ input }) => {
      const { category } = input;
      let models = getAllModels();

      if (category) {
        models = models.filter(model => model.category === category);
      }

      return models.map(model => ({
        id: model.id,
        name: model.name,
        description: model.description,
        creditsPerUse: model.creditsPerUse,
        category: model.category,
        provider: model.provider,
      }));
    }),

  // 检查用户是否有足够积分进行创作
  checkCreditsForCreation: protectedProcedure
    .input(z.object({
      modelId: z.string().optional()
    }))
    .query(async ({ ctx, input }) => {
      const userId = ctx.session.user.id;
      const modelId = input.modelId || getDefaultImageEditingModel().id;
      const requiredCredits = getModelCredits(modelId);
      const hasEnough = await checkCredits(userId, requiredCredits);
      return {
        hasEnoughCredits: hasEnough,
        requiredCredits,
        modelId
      };
    }),

  create: protectedProcedure
    .input(z.object({
      prompt: z.string().min(1),
      imageUrl: z.string().url(),
      modelId: z.string().optional(),
      locale: z.string().optional().default("zh")
    }))
    .mutation(async ({ ctx, input }) => {
      const { prompt, imageUrl, modelId, locale } = input;
      const userId = ctx.session.user.id;
      let transactionId: string | undefined;

      // 获取翻译函数
      const t = await getTranslations({ locale, namespace: 'generation' });

      // 获取模型配置
      const selectedModelId = modelId ?? getDefaultImageEditingModel().id;
      const modelConfig = getModelConfig(selectedModelId);

      if (!modelConfig) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: t('errors.invalidModel'),
        });
      }

      const requiredCredits = modelConfig.creditsPerUse;

      try {
        // 消耗积分（根据模型配置）
        await requireCredits(userId, requiredCredits, `${modelConfig.name} - ${t('result.creditsUsed')}`);

        // 调用fal-ai进行图片处理
        console.log(t('status.aiProcessing'), { prompt, imageUrl, model: modelConfig.name });

        const result = await fal.subscribe(modelConfig.endpoint, {
          input: {
            prompt,
            image_url: imageUrl,
            ...modelConfig.parameters,
          },
          pollInterval: 2000,
          logs: true,
          onQueueUpdate(update) {
            console.log("队列更新:", update);
          },
        });

        console.log("AI处理完成:", result);

        // 检查结果
        if (!result.data?.images?.[0]?.url) {
          throw new Error(t('errors.aiProcessingFailed'));
        }

        const generatedImageUrl = result.data.images[0].url as string;

        // 将生成的图片上传到R2存储
        console.log(t('status.uploadingToStorage'));
        const r2ImageUrl = await uploadImageFromUrl(generatedImageUrl, userId, 'generated');
        console.log(t('status.completed'), r2ImageUrl);

        return {
          success: true,
          originalImageUrl: imageUrl,
          generatedImageUrl: r2ImageUrl,
          prompt,
          modelId: selectedModelId,
          modelName: modelConfig.name,
          creditsUsed: requiredCredits,
          processingTime: (result.data?.timings?.inference as number) ?? 0,
        };

      } catch (error) {
        console.error(t('failed'), error);

        // 退还积分
        try {
          const creditsT = await getTranslations({ locale, namespace: 'credits' });
          await CreditService.refundCredits(
            userId,
            requiredCredits,
            `${modelConfig.name} - ${creditsT('history.description.aiImageEditFailed')}`,
            transactionId
          );
          console.log(`积分已退还: ${requiredCredits}`);
        } catch (refundError) {
          console.error("积分退还失败:", refundError);
        }

        // 抛出用户友好的错误信息
        if (error instanceof Error) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: t('errors.processingError', { message: error.message }),
          });
        } else {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: t('errors.genericError'),
          });
        }
      }
    }),
});