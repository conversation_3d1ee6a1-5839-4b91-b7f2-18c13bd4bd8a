import { TRPCError } from "@trpc/server";
import { CreditService } from "@/server/services/creditService";
import { getCreditConfig } from "@/lib/credits-config";

/**
 * 积分中间件 - 用于需要消耗积分的操作
 */
export async function requireCredits(
  userId: string,
  amount: number,
  description: string,
  relatedId?: string
): Promise<void> {
  // 检查用户是否有足够积分（会自动刷新每日积分）
  const hasEnough = await CreditService.hasEnoughCredits(userId, amount);
  
  if (!hasEnough) {
    throw new TRPCError({
      code: "BAD_REQUEST",
      message: `积分不足，需要 ${amount} 积分`,
    });
  }

  // 消耗积分
  const success = await CreditService.consumeCredits(userId, amount, description, relatedId);
  
  if (!success) {
    throw new TRPCError({
      code: "INTERNAL_SERVER_ERROR",
      message: "积分消耗失败",
    });
  }
}

/**
 * 检查积分但不消耗 - 用于预检查
 */
export async function checkCredits(userId: string, amount: number): Promise<boolean> {
  return await CreditService.hasEnoughCredits(userId, amount);
}

/**
 * 获取用户积分信息 - 会自动刷新每日积分
 */
export async function getUserCreditsInfo(userId: string) {
  return await CreditService.getUserCredits(userId);
}
