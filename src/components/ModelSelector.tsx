"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "motion/react";
import { Button } from "@/components/ui/button";
import { ChevronDown, Sparkles, Coins, Info } from "lucide-react";
import { useTranslations } from "next-intl";

interface ModelOption {
  id: string;
  name: string;
  description: string;
  creditsPerUse: number;
  category: string;
  provider: string;
}

interface ModelSelectorProps {
  models: ModelOption[];
  selectedModelId: string;
  onModelSelect: (modelId: string) => void;
  disabled?: boolean;
  className?: string;
}

export function ModelSelector({
  models,
  selectedModelId,
  onModelSelect,
  disabled = false,
  className = "",
}: ModelSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const t = useTranslations("generation");

  const selectedModel = models.find(model => model.id === selectedModelId) || models[0];

  const handleModelSelect = (modelId: string) => {
    onModelSelect(modelId);
    setIsOpen(false);
  };

  if (!models || models.length === 0) {
    return null;
  }

  return (
    <div className={`relative ${className}`}>
      {/* 选择器按钮 */}
      <Button
        variant="outline"
        onClick={() => setIsOpen(!isOpen)}
        disabled={disabled}
        className="w-full justify-between h-auto p-4 bg-white/80 backdrop-blur-sm border-rose-200/50 hover:border-rose-300/50"
      >
        <div className="flex items-center gap-3">
          <div className="flex items-center justify-center w-8 h-8 bg-gradient-to-r from-rose-500 to-orange-500 rounded-full">
            <Sparkles className="h-4 w-4 text-white" />
          </div>
          <div className="text-left">
            <p className="font-medium text-gray-800">
              {selectedModel?.name || "选择模型"}
            </p>
            <div className="flex items-center gap-2 text-xs text-gray-600">
              <Coins className="h-3 w-3" />
              <span>{selectedModel?.creditsPerUse || 1} 积分/次</span>
            </div>
          </div>
        </div>
        <ChevronDown className={`h-4 w-4 text-gray-500 transition-transform ${
          isOpen ? 'rotate-180' : ''
        }`} />
      </Button>

      {/* 下拉选项 */}
      <AnimatePresence>
        {isOpen && (
          <>
            {/* 背景遮罩 */}
            <div
              className="fixed inset-0 z-10"
              onClick={() => setIsOpen(false)}
            />
            
            {/* 选项列表 */}
            <motion.div
              initial={{ opacity: 0, y: -10, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -10, scale: 0.95 }}
              transition={{ duration: 0.2 }}
              className="absolute top-full left-0 right-0 mt-2 bg-white/95 backdrop-blur-md border border-rose-200/50 rounded-xl shadow-lg z-20 max-h-80 overflow-y-auto"
            >
              <div className="p-2">
                {models.map((model) => (
                  <motion.button
                    key={model.id}
                    onClick={() => handleModelSelect(model.id)}
                    className={`w-full text-left p-3 rounded-lg transition-all duration-200 ${
                      model.id === selectedModelId
                        ? 'bg-gradient-to-r from-rose-50 to-orange-50 border border-rose-200'
                        : 'hover:bg-gray-50 border border-transparent'
                    }`}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className="font-medium text-gray-800">
                            {model.name}
                          </h4>
                          {model.id === selectedModelId && (
                            <div className="w-2 h-2 bg-rose-500 rounded-full" />
                          )}
                        </div>
                        <p className="text-sm text-gray-600 mb-2">
                          {model.description}
                        </p>
                        <div className="flex items-center gap-4 text-xs text-gray-500">
                          <div className="flex items-center gap-1">
                            <Coins className="h-3 w-3" />
                            <span>{model.creditsPerUse} 积分</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Info className="h-3 w-3" />
                            <span>{model.provider}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </motion.button>
                ))}
              </div>
              
              {/* 底部提示 */}
              <div className="border-t border-gray-200/50 p-3 bg-gray-50/50">
                <p className="text-xs text-gray-500 text-center">
                  不同模型的积分消耗和效果可能不同
                </p>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  );
}
