'use client';

import { useState } from "react";
import Image from "next/image";
import * as motion from "motion/react-client";
import {
  <PERSON><PERSON><PERSON>,
  ChevronLeft,
  ChevronR<PERSON>,
  <PERSON>Pointer,
  Eye,
  Columns2
} from "lucide-react";
import { useTranslations } from 'next-intl';
import { useIsClient } from "@/hooks/useIsClient";

type ComparisonMode = 'hover' | 'slider' | 'click' | 'side-by-side';

interface ImageComparisonProps {
  beforeImage: string;
  afterImage: string;
  title: string;
  className?: string;
}

export function ImageComparison({
  beforeImage,
  afterImage,
  title,
  className = "",
}: ImageComparisonProps) {
  const isClient = useIsClient();
  const [isHovered, setIsHovered] = useState(false);
  const [showAfter, setShowAfter] = useState(false);
  const [sliderPosition, setSliderPosition] = useState(50);
  const [comparisonMode, setComparisonMode] = useState<ComparisonMode>('hover');
  const [isDragging, setIsDragging] = useState(false);
  const t = useTranslations('gallery.beforeAfter');

  const handleSliderChange = (e: React.MouseEvent) => {
    if (!isDragging) return;
    const rect = e.currentTarget.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));
    setSliderPosition(percentage);
  };

  const modes = [
    { key: 'hover' as ComparisonMode, icon: MousePointer, label: 'Hover' },
    { key: 'slider' as ComparisonMode, icon: ChevronLeft, label: 'Slider' },
    { key: 'click' as ComparisonMode, icon: Eye, label: 'Click' },
    { key: 'side-by-side' as ComparisonMode, icon: Columns2, label: 'Side' },
  ];

  if (!isClient) {
    return (
      <div className={`relative ${className}`}>
        <div className="relative aspect-[4/3] overflow-hidden">
          <Image
            src={beforeImage}
            alt={`${title} - ${t('before')}`}
            fill
            className="object-cover"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
        </div>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>
      {/* Comparison Mode Selector */}
      <div className="absolute top-3 right-3 z-20 flex gap-1 bg-black/60 backdrop-blur-sm rounded-lg p-1">
        {modes.map((mode) => {
          const Icon = mode.icon;
          return (
            <button
              key={mode.key}
              onClick={() => {
                setComparisonMode(mode.key);
                if (mode.key === 'slider') setSliderPosition(50);
                if (mode.key === 'click') setShowAfter(false);
              }}
              className={`p-1.5 rounded transition-all ${
                comparisonMode === mode.key
                  ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white'
                  : 'text-white/70 hover:text-white hover:bg-white/10'
              }`}
              title={mode.label}
            >
              <Icon className="w-3 h-3" />
            </button>
          );
        })}
      </div>

      {/* Image Comparison Area */}
      <div
        className="relative aspect-[4/3] overflow-hidden cursor-pointer group"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onClick={() => {
          if (comparisonMode === 'click') {
            setShowAfter(!showAfter);
          }
        }}
        onMouseMove={comparisonMode === 'slider' ? handleSliderChange : undefined}
        onMouseDown={() => comparisonMode === 'slider' && setIsDragging(true)}
        onMouseUp={() => setIsDragging(false)}
      >
        {comparisonMode === 'side-by-side' ? (
          <>
            {/* Side by Side Layout */}
            <div className="absolute inset-0 flex">
              <div className="relative w-1/2 overflow-hidden">
                <Image
                  src={beforeImage}
                  alt={`${title} - ${t('before')}`}
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 50vw, (max-width: 1200px) 25vw, 16vw"
                />
                <div className="absolute bottom-3 left-3 rounded-lg bg-black/80 backdrop-blur-sm px-3 py-1.5 text-xs font-medium text-white border border-white/20">
                  {t('before')}
                </div>
              </div>
              <div className="relative w-1/2 overflow-hidden">
                <Image
                  src={afterImage}
                  alt={`${title} - ${t('after')}`}
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 50vw, (max-width: 1200px) 25vw, 16vw"
                />
                <div className="absolute bottom-3 right-3 rounded-lg bg-gradient-to-r from-blue-600 to-purple-600 backdrop-blur-sm px-3 py-1.5 text-xs font-medium text-white shadow-lg">
                  {t('after')}
                </div>
              </div>
            </div>
            {/* Divider Line */}
            <div className="absolute top-0 bottom-0 left-1/2 w-0.5 bg-white/30 z-10" />
          </>
        ) : comparisonMode === 'slider' ? (
          <>
            {/* Slider Comparison */}
            <div className="absolute inset-0">
              <Image
                src={beforeImage}
                alt={`${title} - ${t('before')}`}
                fill
                className="object-cover"
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              />
            </div>
            <motion.div
              className="absolute inset-0 overflow-hidden"
              style={{ clipPath: `inset(0 ${100 - sliderPosition}% 0 0)` }}
            >
              <Image
                src={afterImage}
                alt={`${title} - ${t('after')}`}
                fill
                className="object-cover"
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              />
            </motion.div>
            {/* Slider Handle */}
            <motion.div
              className="absolute top-0 bottom-0 w-1 bg-gradient-to-b from-blue-500 to-purple-500 shadow-lg z-10 flex items-center justify-center cursor-ew-resize"
              style={{ left: `${sliderPosition}%`, transform: 'translateX(-50%)' }}
              whileHover={{ scale: 1.1 }}
              transition={{ duration: 0.2 }}
            >
              <motion.div 
                className="w-8 h-8 bg-white rounded-full shadow-lg flex items-center justify-center border-2 border-blue-500"
                animate={{ rotate: isDragging ? 180 : 0 }}
                transition={{ duration: 0.3 }}
              >
                <ChevronLeft className="w-3 h-3 text-blue-600" />
                <ChevronRight className="w-3 h-3 text-blue-600" />
              </motion.div>
            </motion.div>
            {/* Labels */}
            <div className="absolute bottom-3 left-3 rounded-lg bg-black/80 backdrop-blur-sm px-3 py-1.5 text-xs font-medium text-white border border-white/20">
              {t('before')}
            </div>
            <div className="absolute bottom-3 right-3 rounded-lg bg-gradient-to-r from-blue-600 to-purple-600 backdrop-blur-sm px-3 py-1.5 text-xs font-medium text-white shadow-lg">
              {t('after')}
            </div>
          </>
        ) : (
          <>
            {/* Hover/Click Comparison */}
            {/* Before Image - Always visible as base */}
            <div className="absolute inset-0">
              <Image
                src={beforeImage}
                alt={`${title} - ${t('before')}`}
                fill
                className="object-cover group-hover:scale-105 transition-transform duration-700"
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              />
              <div className="absolute bottom-3 left-3 rounded-lg bg-black/80 backdrop-blur-sm px-3 py-1.5 text-xs font-medium text-white border border-white/20">
                {t('before')}
              </div>
            </div>

            {/* After Image - Shows on hover/click */}
            <motion.div
              className="absolute inset-0"
              animate={{
                opacity: (comparisonMode === 'hover' && isHovered) || (comparisonMode === 'click' && showAfter) ? 1 : 0
              }}
              transition={{ duration: comparisonMode === 'hover' ? 0.6 : 0.3, ease: "easeInOut" }}
            >
              <Image
                src={afterImage}
                alt={`${title} - ${t('after')}`}
                fill
                className="object-cover group-hover:scale-105 transition-transform duration-700"
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              />
              <div className="absolute bottom-3 left-3 rounded-lg bg-gradient-to-r from-blue-600 to-purple-600 backdrop-blur-sm px-3 py-1.5 text-xs font-medium text-white shadow-lg">
                {t('after')}
              </div>
            </motion.div>
          </>
        )}
      </div>
    </div>
  );
}