import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { ChevronRight, Home } from 'lucide-react';

interface BreadcrumbItem {
  label: string;
  href?: string;
  isActive?: boolean;
}

interface BreadcrumbProps {
  items: BreadcrumbItem[];
  locale: string;
}

export function Breadcrumb({ items, locale }: BreadcrumbProps) {
  const t = useTranslations('common');

  const allItems = [
    { label: t('home'), href: locale === 'en' ? '/' : `/${locale}` },
    ...items
  ];

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": allItems.map((item, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": item.label,
      ...(item.href && { "item": `https://fluxpix.ai${item.href}` })
    }))
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData)
        }}
      />
      <nav aria-label="Breadcrumb" className="mb-8">
        <ol className="flex items-center space-x-2 text-sm text-muted-foreground">
          {allItems.map((item, index) => (
            <li key={index} className="flex items-center">
              {index === 0 && <Home className="w-4 h-4 mr-1" />}
              {item.href && !item.isActive ? (
                <Link
                  href={item.href}
                  className="hover:text-foreground transition-colors"
                >
                  {item.label}
                </Link>
              ) : (
                <span className={item.isActive ? 'text-foreground font-medium' : ''}>
                  {item.label}
                </span>
              )}
              {index < allItems.length - 1 && (
                <ChevronRight className="w-4 h-4 mx-2 text-muted-foreground/60" />
              )}
            </li>
          ))}
        </ol>
      </nav>
    </>
  );
}