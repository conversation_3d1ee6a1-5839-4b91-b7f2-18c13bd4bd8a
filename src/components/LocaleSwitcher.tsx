"use client";

import { useLocale, useTranslations } from "next-intl";
import { routing, usePathname, useRouter } from "@/i18n/routing";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from "@/components/ui/select";
import { useParams } from "next/navigation";
import { Globe } from "lucide-react";

export function LocaleSwitcher() {
  const locale = useLocale();
  const t = useTranslations('locale');

  const router = useRouter();
  const pathname = usePathname();
  const params = useParams();

  const handleChange = (value: string) => {
    router.replace(
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-expect-error
      { pathname, params },
      { locale: value },
    );
  };

  return (
    <Select defaultValue={locale} onValueChange={handleChange}>
      <SelectTrigger
        className="h-8 w-10 px-1 border border-rose-400/50 bg-gradient-to-r from-rose-500/80 to-orange-500/80 py-0 text-white transition-all duration-200 hover:from-rose-400 hover:to-orange-400 hover:shadow-md focus:ring-2 focus:ring-0 focus:ring-offset-0"
        aria-label={t('switchLanguage')}
      >
        <Globe className="h-4 w-4" />
        <span className="sr-only">{t('switchLanguage')}</span>
      </SelectTrigger>
      <SelectContent
        className="z-[200] min-w-[100px] rounded-lg border-rose-200/50 bg-white/95 text-gray-800 shadow-lg backdrop-blur-sm"
        align="end"
      >
        {routing.locales.map((cur) => (
          <SelectItem
            key={cur}
            value={cur}
            className="py-1.5 text-sm text-gray-700 hover:bg-rose-50 hover:text-rose-700 focus:bg-rose-50 focus:text-rose-700 data-[state=checked]:bg-rose-100 data-[state=checked]:text-rose-800 data-[state=checked]:font-medium"
          >
            {t(`languages.${cur}`)}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}
