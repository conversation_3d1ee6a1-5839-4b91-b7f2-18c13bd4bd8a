"use client";

import { api } from "@/trpc/react";
import { Sparkles, Coins } from "lucide-react";
import { motion } from "motion/react";
import { useTranslations } from "next-intl";

interface CreditDisplayProps {
  className?: string;
}

export function CreditDisplay({ className = "" }: CreditDisplayProps) {
  const { data: userWithCredits, isLoading } = api.credits.getUserWithCredits.useQuery();
  const t = useTranslations("credits");

  if (isLoading) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <div className="w-6 h-6 bg-gray-200 rounded-full animate-pulse" />
        <div className="w-12 h-4 bg-gray-200 rounded animate-pulse" />
      </div>
    );
  }

  if (!userWithCredits?.credits) {
    return null;
  }

  const { userType, credits } = userWithCredits;

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className={`flex items-center gap-2 ${className}`}
    >
      <div className="flex items-center gap-1 px-3 py-1.5 bg-gradient-to-r from-amber-100 to-orange-100 border border-amber-200 rounded-full">
        <Coins className="h-4 w-4 text-amber-600" />
        <span className="text-sm font-medium text-amber-800">
          {credits.availableCredits}
        </span>
      </div>

      {/* 显示用户类型 */}
      <div className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs ${
        userType === 'free'
          ? 'bg-gray-100 border border-gray-200 text-gray-700'
          : 'bg-purple-100 border border-purple-200 text-purple-700'
      }`}>
        <span>{t(`userType.${userType}`)}</span>
      </div>

      {/* 显示今日是否已刷新（仅免费用户） */}
      {userType === 'free' && isToday(credits.lastDailyRefresh) && (
        <div className="flex items-center gap-1 px-2 py-1 bg-green-100 border border-green-200 rounded-full">
          <Sparkles className="h-3 w-3 text-green-600" />
          <span className="text-xs text-green-700">{t('display.refreshedToday')}</span>
        </div>
      )}
    </motion.div>
  );
}

// 检查日期是否是今天
function isToday(date: Date | string): boolean {
  const today = new Date();
  const checkDate = new Date(date);
  
  return (
    checkDate.getDate() === today.getDate() &&
    checkDate.getMonth() === today.getMonth() &&
    checkDate.getFullYear() === today.getFullYear()
  );
}
