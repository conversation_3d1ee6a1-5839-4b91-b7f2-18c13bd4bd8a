"use client";

import { motion, AnimatePresence } from "motion/react";
import { Button } from "@/components/ui/button";
import { AlertCircle, Coins, Clock } from "lucide-react";
import { api } from "@/trpc/react";
import { useTranslations } from "next-intl";

interface InsufficientCreditsModalProps {
  isOpen: boolean;
  onClose: () => void;
  requiredCredits: number;
}

export function InsufficientCreditsModal({
  isOpen,
  onClose,
  requiredCredits,
}: InsufficientCreditsModalProps) {
  const { data: credits } = api.credits.getUserCredits.useQuery();
  const t = useTranslations("credits");

  const getTimeUntilNextRefresh = () => {
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);
    
    const diff = tomorrow.getTime() - now.getTime();
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    
    return { hours, minutes };
  };

  const { hours, minutes } = getTimeUntilNextRefresh();

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* 背景遮罩 */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50"
            onClick={onClose}
          />
          
          {/* 模态框 */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            className="fixed inset-0 z-50 flex items-center justify-center p-4"
          >
            <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full p-6">
              {/* 图标 */}
              <div className="flex justify-center mb-4">
                <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
                  <AlertCircle className="h-8 w-8 text-red-600" />
                </div>
              </div>

              {/* 标题 */}
              <h3 className="text-xl font-bold text-center text-gray-900 mb-2">
                {t('insufficient.title')}
              </h3>

              {/* 内容 */}
              <div className="text-center text-gray-600 mb-6">
                <p className="mb-4">
                  {t('insufficient.message', {
                    required: requiredCredits,
                    available: credits?.availableCredits || 0
                  })}
                </p>
                
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-center justify-center gap-2 mb-2">
                    <Clock className="h-4 w-4 text-blue-600" />
                    <span className="text-sm font-medium text-blue-800">
                      {t('insufficient.dailyRefresh')}
                    </span>
                  </div>
                  <p className="text-sm text-blue-700">
                    {t('insufficient.timeUntilRefresh', { hours, minutes })}
                  </p>
                  <p className="text-xs text-blue-600 mt-1">
                    {t('insufficient.dailyAmount')}
                  </p>
                </div>
              </div>

              {/* 按钮 */}
              <div className="flex gap-3">
                <Button
                  variant="outline"
                  onClick={onClose}
                  className="flex-1"
                >
                  {t('insufficient.tryLater')}
                </Button>
                <Button
                  onClick={onClose}
                  className="flex-1 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-400 hover:to-purple-400"
                >
                  {t('insufficient.learnMore')}
                </Button>
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
}
