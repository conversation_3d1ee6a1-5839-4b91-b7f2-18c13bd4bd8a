/**
 * AI模型配置
 * 包含模型名称、积分消耗、参数等信息
 */

export interface ModelConfig {
  id: string;
  name: string;
  description: string;
  creditsPerUse: number;
  category: 'image-editing' | 'text-to-image' | 'image-to-image';
  provider: 'fal-ai' | 'openai' | 'stability';
  endpoint: string;
  maxImageSize?: number;
  supportedFormats?: string[];
  parameters?: {
    num_inference_steps?: number;
    guidance_scale?: number;
    num_images?: number;
    enable_safety_checker?: boolean;
  };
}

/**
 * 模型配置列表
 */
export const MODEL_CONFIGS: Record<string, ModelConfig> = {
  // Flux 系列模型
  'flux-kontext-dev': {
    id: 'flux-kontext-dev',
    name: 'Flux Kontext Dev',
    description: '开发版图片编辑模型，适合测试和开发',
    creditsPerUse: 1,
    category: 'image-editing',
    provider: 'fal-ai',
    endpoint: 'fal-ai/flux-kontext/dev',
    parameters: {
      num_inference_steps: 28,
      guidance_scale: 3.5,
      num_images: 1,
      enable_safety_checker: true,
    },
  },
  'flux-dev': {
    id: 'flux-dev',
    name: 'Flux Dev',
    description: '基础图片生成模型',
    creditsPerUse: 2,
    category: 'text-to-image',
    provider: 'fal-ai',
    endpoint: 'fal-ai/flux/dev',
    parameters: {
      num_inference_steps: 28,
      guidance_scale: 3.5,
      num_images: 1,
    },
  },
  'flux-pro': {
    id: 'flux-pro',
    name: 'Flux 1.1 Pro',
    description: '专业级图片生成，更快更好的质量',
    creditsPerUse: 5,
    category: 'text-to-image',
    provider: 'fal-ai',
    endpoint: 'fal-ai/flux-pro/v1.1',
    parameters: {
      num_inference_steps: 25,
      guidance_scale: 3.0,
      num_images: 1,
      enable_safety_checker: true,
    },
  },
  'flux-ultra': {
    id: 'flux-ultra',
    name: 'Flux 1.1 Ultra',
    description: '超高分辨率和真实感，适合专业创作',
    creditsPerUse: 10,
    category: 'text-to-image',
    provider: 'fal-ai',
    endpoint: 'fal-ai/flux-pro/v1.1-ultra',
    parameters: {
      num_inference_steps: 30,
      guidance_scale: 2.5,
      num_images: 1,
      enable_safety_checker: true,
    },
  },
  'flux-fill': {
    id: 'flux-fill',
    name: 'Flux Fill',
    description: '图片编辑和扩展功能，支持精确修改',
    creditsPerUse: 3,
    category: 'image-editing',
    provider: 'fal-ai',
    endpoint: 'fal-ai/flux/fill',
    parameters: {
      num_inference_steps: 28,
      guidance_scale: 3.5,
      enable_safety_checker: true,
    },
  },
};

/**
 * 获取模型配置
 */
export function getModelConfig(modelId: string): ModelConfig | null {
  return MODEL_CONFIGS[modelId] || null;
}

/**
 * 获取模型积分消耗
 */
export function getModelCredits(modelId: string): number {
  const config = getModelConfig(modelId);
  return config?.creditsPerUse || 1; // 默认1积分
}

/**
 * 获取所有模型配置
 */
export function getAllModels(): ModelConfig[] {
  return Object.values(MODEL_CONFIGS);
}

/**
 * 根据类别获取模型
 */
export function getModelsByCategory(category: ModelConfig['category']): ModelConfig[] {
  return getAllModels().filter(model => model.category === category);
}

/**
 * 获取图片编辑模型
 */
export function getImageEditingModels(): ModelConfig[] {
  return getModelsByCategory('image-editing');
}

/**
 * 获取文本生成图片模型
 */
export function getTextToImageModels(): ModelConfig[] {
  return getModelsByCategory('text-to-image');
}

/**
 * 验证模型是否存在
 */
export function isValidModel(modelId: string): boolean {
  return modelId in MODEL_CONFIGS;
}

/**
 * 获取默认模型（用于图片编辑）
 */
export function getDefaultImageEditingModel(): ModelConfig {
  return MODEL_CONFIGS['flux-kontext-dev']!;
}

/**
 * 获取默认模型（用于文本生成图片）
 */
export function getDefaultTextToImageModel(): ModelConfig {
  return MODEL_CONFIGS['flux-dev']!;
}
