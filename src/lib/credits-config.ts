/**
 * 积分系统配置
 * 包含不同用户类型的积分赠送、限制等配置
 */

export interface CreditConfig {
  userType: 'free' | 'premium' | 'pro';
  dailyCredits: number;
  maxDailyCredits: number;
  newUserBonus: number;
  referralBonus: number;
  purchaseMultiplier: number;
  features: {
    canSavePrivate: boolean;
    canUseCommercially: boolean;
    hasAds: boolean;
    maxStorageDays: number;
    prioritySupport: boolean;
  };
}

/**
 * 积分配置列表
 */
export const CREDIT_CONFIGS: Record<string, CreditConfig> = {
  free: {
    userType: 'free',
    dailyCredits: 10,           // 每日免费积分
    maxDailyCredits: 10,        // 每日最大积分（防止累积）
    newUserBonus: 20,           // 新用户奖励积分
    referralBonus: 50,          // 推荐奖励积分
    purchaseMultiplier: 1,      // 购买积分倍数
    features: {
      canSavePrivate: false,    // 不能私有保存
      canUseCommercially: false, // 不能商业使用
      hasAds: true,             // 有广告
      maxStorageDays: 30,       // 结果保存30天
      prioritySupport: false,   // 无优先支持
    },
  },
  premium: {
    userType: 'premium',
    dailyCredits: 50,           // 每日免费积分
    maxDailyCredits: 100,       // 每日最大积分
    newUserBonus: 100,          // 新用户奖励积分
    referralBonus: 100,         // 推荐奖励积分
    purchaseMultiplier: 1.2,    // 购买积分1.2倍
    features: {
      canSavePrivate: true,     // 可以私有保存
      canUseCommercially: true, // 可以商业使用
      hasAds: false,            // 无广告
      maxStorageDays: 365,      // 结果保存1年
      prioritySupport: false,   // 无优先支持
    },
  },
  pro: {
    userType: 'pro',
    dailyCredits: 100,          // 每日免费积分
    maxDailyCredits: 200,       // 每日最大积分
    newUserBonus: 200,          // 新用户奖励积分
    referralBonus: 200,         // 推荐奖励积分
    purchaseMultiplier: 1.5,    // 购买积分1.5倍
    features: {
      canSavePrivate: true,     // 可以私有保存
      canUseCommercially: true, // 可以商业使用
      hasAds: false,            // 无广告
      maxStorageDays: -1,       // 永久保存
      prioritySupport: true,    // 优先支持
    },
  },
};

/**
 * 积分操作类型配置
 */
export interface CreditActionConfig {
  type: 'daily_grant' | 'usage' | 'refund' | 'purchase' | 'bonus' | 'referral';
  name: string;
  description: string;
  color: string;
  icon: string;
}

export const CREDIT_ACTION_CONFIGS: Record<string, CreditActionConfig> = {
  daily_grant: {
    type: 'daily_grant',
    name: '每日赠送',
    description: '每日免费积分',
    color: 'green',
    icon: 'gift',
  },
  usage: {
    type: 'usage',
    name: '消耗',
    description: 'AI功能使用',
    color: 'red',
    icon: 'minus',
  },
  refund: {
    type: 'refund',
    name: '退还',
    description: '操作失败退还',
    color: 'blue',
    icon: 'rotate-ccw',
  },
  purchase: {
    type: 'purchase',
    name: '购买',
    description: '积分包购买',
    color: 'purple',
    icon: 'shopping-cart',
  },
  bonus: {
    type: 'bonus',
    name: '奖励',
    description: '活动奖励积分',
    color: 'yellow',
    icon: 'star',
  },
  referral: {
    type: 'referral',
    name: '推荐',
    description: '推荐用户奖励',
    color: 'indigo',
    icon: 'users',
  },
};

/**
 * 获取用户类型的积分配置
 */
export function getCreditConfig(userType: 'free' | 'premium' | 'pro'): CreditConfig {
  return CREDIT_CONFIGS[userType] || CREDIT_CONFIGS.free;
}

/**
 * 获取每日积分数量
 */
export function getDailyCredits(userType: 'free' | 'premium' | 'pro'): number {
  return getCreditConfig(userType).dailyCredits;
}

/**
 * 获取新用户奖励积分
 */
export function getNewUserBonus(userType: 'free' | 'premium' | 'pro'): number {
  return getCreditConfig(userType).newUserBonus;
}

/**
 * 获取推荐奖励积分
 */
export function getReferralBonus(userType: 'free' | 'premium' | 'pro'): number {
  return getCreditConfig(userType).referralBonus;
}

/**
 * 获取最大每日积分
 */
export function getMaxDailyCredits(userType: 'free' | 'premium' | 'pro'): number {
  return getCreditConfig(userType).maxDailyCredits;
}

/**
 * 检查用户是否可以获得每日积分
 */
export function canReceiveDailyCredits(
  userType: 'free' | 'premium' | 'pro',
  currentCredits: number
): boolean {
  const config = getCreditConfig(userType);
  return currentCredits < config.maxDailyCredits;
}

/**
 * 计算应该赠送的每日积分数量
 */
export function calculateDailyCreditsToGrant(
  userType: 'free' | 'premium' | 'pro',
  currentCredits: number
): number {
  const config = getCreditConfig(userType);
  
  if (currentCredits >= config.maxDailyCredits) {
    return 0; // 已达到最大积分，不再赠送
  }
  
  const remainingSpace = config.maxDailyCredits - currentCredits;
  return Math.min(config.dailyCredits, remainingSpace);
}

/**
 * 获取积分操作配置
 */
export function getCreditActionConfig(actionType: string): CreditActionConfig | null {
  return CREDIT_ACTION_CONFIGS[actionType] || null;
}

/**
 * 获取所有用户类型配置
 */
export function getAllCreditConfigs(): CreditConfig[] {
  return Object.values(CREDIT_CONFIGS);
}

/**
 * 验证用户类型
 */
export function isValidUserType(userType: string): userType is 'free' | 'premium' | 'pro' {
  return userType in CREDIT_CONFIGS;
}
