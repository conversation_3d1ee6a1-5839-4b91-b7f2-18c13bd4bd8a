import { Metadata } from "next";
import { getTranslations } from "next-intl/server";
import { routing } from "@/i18n/routing";

/**
 * 页面类型定义
 */
export type PageType = 'home' | 'generate' | 'notFound';

/**
 * 网站配置
 */
const SITE_CONFIG = {
  name: 'Flux Pix',
  domain: 'https://fluxpix.ai',
  defaultImage: '/og-image.png',
  twitterHandle: '@fluxpix',
} as const;

/**
 * 获取本地化的 URL
 */
function getLocalizedUrl(locale: string, path: string = ''): string {
  const basePath = locale === routing.defaultLocale ? '' : `/${locale}`;
  return `${SITE_CONFIG.domain}${basePath}${path}`;
}

/**
 * 生成语言替代链接
 */
function generateLanguageAlternates(path: string = ''): Record<string, string> {
  const alternates: Record<string, string> = {};
  
  // 为每个支持的语言生成链接
  routing.locales.forEach(locale => {
    alternates[locale] = getLocalizedUrl(locale, path);
  });
  
  // 设置默认语言
  alternates['x-default'] = getLocalizedUrl(routing.defaultLocale, path);
  
  return alternates;
}

/**
 * 生成页面 metadata
 */
export async function generatePageMetadata(
  locale: string,
  pageType: PageType,
  path: string = ''
): Promise<Metadata> {
  // 验证 locale 是否有效
  if (!routing.locales.includes(locale as any)) {
    locale = routing.defaultLocale;
  }

  // 获取翻译
  const t = await getTranslations({ locale, namespace: 'metadata' });
  
  // 获取页面特定的翻译
  const pageMetadata = t.raw(pageType) as {
    title: string;
    description: string;
    keywords: string[];
  };

  const canonicalUrl = getLocalizedUrl(locale, path);
  const languageAlternates = generateLanguageAlternates(path);

  const metadata: Metadata = {
    title: pageMetadata.title,
    description: pageMetadata.description,
    keywords: pageMetadata.keywords,
    authors: [{ name: `${SITE_CONFIG.name} Team` }],
    creator: SITE_CONFIG.name,
    publisher: SITE_CONFIG.name,
    robots: pageType === 'notFound' ? 'noindex, nofollow' : 'index, follow',
    
    // Open Graph
    openGraph: {
      type: 'website',
      siteName: SITE_CONFIG.name,
      title: pageMetadata.title,
      description: pageMetadata.description,
      url: canonicalUrl,
      images: [SITE_CONFIG.defaultImage],
      locale: locale === 'zh' ? 'zh_CN' : 'en_US',
      alternateLocale: routing.locales
        .filter(l => l !== locale)
        .map(l => l === 'zh' ? 'zh_CN' : 'en_US'),
    },

    // Twitter Card
    twitter: {
      card: 'summary_large_image',
      title: pageMetadata.title,
      description: pageMetadata.description,
      images: [SITE_CONFIG.defaultImage],
      creator: SITE_CONFIG.twitterHandle,
    },

    // 规范化和语言替代
    alternates: {
      canonical: canonicalUrl,
      languages: languageAlternates,
    },

    // 其他元数据
    viewport: 'width=device-width, initial-scale=1',
    themeColor: '#3b82f6',
  };

  return metadata;
}

/**
 * 生成结构化数据
 */
export async function generateStructuredData(
  locale: string,
  pageType: PageType
): Promise<object> {
  const t = await getTranslations({ locale, namespace: 'metadata' });
  const pageMetadata = t.raw(pageType) as {
    title: string;
    description: string;
    keywords: string[];
  };

  const baseStructuredData = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": SITE_CONFIG.name,
    "description": pageMetadata.description,
    "url": SITE_CONFIG.domain,
    "applicationCategory": "MultimediaApplication",
    "operatingSystem": "Web",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "author": {
      "@type": "Organization",
      "name": `${SITE_CONFIG.name} Team`
    },
    "inLanguage": routing.locales.map(locale => ({
      "@type": "Language",
      "name": locale === 'zh' ? 'Chinese' : 'English',
      "alternateName": locale
    }))
  };

  // 根据页面类型添加特定的结构化数据
  if (pageType === 'home' || pageType === 'generate') {
    return {
      ...baseStructuredData,
      "featureList": locale === 'zh' ? [
        "AI智能去水印",
        "背景替换", 
        "对象移除",
        "图片编辑",
        "在线处理"
      ] : [
        "AI Watermark Removal",
        "Background Replacement",
        "Object Removal", 
        "Image Editing",
        "Online Processing"
      ]
    };
  }

  return baseStructuredData;
}

/**
 * 便捷函数：为首页生成 metadata
 */
export async function generateHomeMetadata(locale: string): Promise<Metadata> {
  return generatePageMetadata(locale, 'home');
}

/**
 * 便捷函数：为生成页面生成 metadata
 */
export async function generateGenerateMetadata(locale: string): Promise<Metadata> {
  return generatePageMetadata(locale, 'generate', '/generate');
}

/**
 * 便捷函数：为 404 页面生成 metadata
 */
export async function generateNotFoundMetadata(locale: string): Promise<Metadata> {
  return generatePageMetadata(locale, 'notFound');
}
