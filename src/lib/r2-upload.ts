import { PutObjectCommand } from '@aws-sdk/client-s3';
import { R2 } from '@/lib/r2';
import { env } from '@/env';
import crypto from 'crypto';

// 生成一个随机的文件名
const generateFileName = (bytes = 16) => crypto.randomBytes(bytes).toString('hex');

/**
 * 从URL下载图片并上传到R2
 */
export async function uploadImageFromUrl(
  imageUrl: string, 
  userId: string, 
  prefix = 'generated'
): Promise<string> {
  try {
    // 下载图片
    const response = await fetch(imageUrl);
    if (!response.ok) {
      throw new Error(`Failed to fetch image: ${response.statusText}`);
    }

    const imageBuffer = await response.arrayBuffer();
    const contentType = response.headers.get('content-type') || 'image/jpeg';
    
    // 根据content-type确定文件扩展名
    const extension = contentType.includes('png') ? 'png' : 
                     contentType.includes('webp') ? 'webp' : 'jpg';
    
    // 生成文件路径
    const key = `${userId}/${prefix}/${generateFileName()}.${extension}`;

    // 上传到R2
    const command = new PutObjectCommand({
      Bucket: env.R2_BUCKET_NAME,
      Key: key,
      Body: new Uint8Array(imageBuffer),
      ContentType: contentType,
      ContentLength: imageBuffer.byteLength,
    });

    await R2.send(command);

    // 返回公共URL
    const publicUrl = `${env.NEXT_PUBLIC_R2_PUBLIC_URL}/${key}`;
    return publicUrl;
  } catch (error) {
    console.error('Failed to upload image to R2:', error);
    throw new Error('图片上传失败');
  }
}

/**
 * 直接上传Buffer到R2
 */
export async function uploadBufferToR2(
  buffer: Buffer | Uint8Array,
  userId: string,
  contentType: string,
  prefix = 'generated'
): Promise<string> {
  try {
    // 根据content-type确定文件扩展名
    const extension = contentType.includes('png') ? 'png' : 
                     contentType.includes('webp') ? 'webp' : 'jpg';
    
    // 生成文件路径
    const key = `${userId}/${prefix}/${generateFileName()}.${extension}`;

    // 上传到R2
    const command = new PutObjectCommand({
      Bucket: env.R2_BUCKET_NAME,
      Key: key,
      Body: buffer,
      ContentType: contentType,
      ContentLength: buffer.byteLength,
    });

    await R2.send(command);

    // 返回公共URL
    const publicUrl = `${env.NEXT_PUBLIC_R2_PUBLIC_URL}/${key}`;
    return publicUrl;
  } catch (error) {
    console.error('Failed to upload buffer to R2:', error);
    throw new Error('图片上传失败');
  }
}
