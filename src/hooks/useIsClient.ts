"use client";

import { useEffect, useState } from "react";

/**
 * Hook to check if the component is running on the client side
 * This helps prevent hydration mismatches by ensuring client-only code
 * only runs after hydration is complete
 */
export function useIsClient() {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return isClient;
}
