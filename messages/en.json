{"navbar": {"features": "Features", "pricing": "Pricing", "gallery": "Gallery", "login": "Log in", "signup": "Sign up", "dashboard": "Dashboard", "openMenu": "Open menu", "closeMenu": "Close menu", "logout": "Log out", "loginWithGoogle": "Log in with Google", "generate": "Generate", "profile": "Profile", "signOut": "Sign out", "signInWithGoogle": "Sign in with Google"}, "hero": {"title": "AI Image Editor", "subtitle": "Describe what you want, AI edits your images", "getStartedButton": "Start Editing", "learnMoreButton": "View Examples", "typingText": "Describe the effect you want, AI makes it happen", "typingMainPart": "Describe the effect you want,", "features": {"removeWatermark": {"title": "Smart Watermark Removal", "description": "Remove watermarks and unwanted elements with one click"}, "backgroundChange": {"title": "Background Replacement", "description": "Easily change image backgrounds with any scene description"}, "objectRemoval": {"title": "Object Removal", "description": "Precisely remove any objects and automatically fill backgrounds"}}}, "features": {"sectionTitle": "Editing Features", "sectionSubtitle": "Simple descriptions, easy editing", "removeWatermark": {"title": "Smart Watermark Removal", "description": "Automatically identify and remove watermarks, logos, and unwanted text while maintaining natural image quality", "details": {"0": "Intelligently identify various watermark types", "1": "Maintain original image quality", "2": "Support batch processing"}}, "backgroundEdit": {"title": "Background Editing", "description": "Replace backgrounds, remove backgrounds, or add new scenes with any background description", "details": {"0": "One-click background replacement", "1": "Smart edge detection", "2": "Multiple background templates"}}, "objectRemoval": {"title": "Object Removal", "description": "Precisely remove any objects, people, or elements from images and intelligently fill blank areas", "details": {"0": "Precisely remove unwanted objects", "1": "Automatic background filling", "2": "Seamless processing"}}, "faceRetouch": {"title": "Portrait Enhancement", "description": "Natural portrait enhancement effects, remove blemishes, adjust skin tone, optimize facial features", "details": {"0": "Natural beauty enhancement effects", "1": "Preserve facial features", "2": "Professional-grade retouching"}}, "styleTransfer": {"title": "Style Transfer", "description": "Transform images into different artistic styles like oil painting, watercolor, sketch, etc.", "details": {"0": "Artistic style conversion", "1": "Multiple filter effects", "2": "Creative image generation"}}, "textReplacement": {"title": "Text Replacement", "description": "Intelligently identify and replace text content in images while maintaining original fonts and styles", "details": {"0": "Smart text recognition", "1": "Seamless text replacement", "2": "Maintain font style"}}, "ctaTitle": "Ready to start editing your images?", "ctaSubtitle": "Simply upload an image, describe your editing needs, and AI will complete professional-grade image processing for you", "ctaButton": "Start Editing Now"}, "cta": {"title": "Start Editing Images", "subtitle": "Upload image, describe effect, AI makes it happen", "signupButton": "Get Started", "learnMoreButton": "Learn More", "loginHint": "No registration required"}, "pricing": {"sectionTitle": "Simple and Transparent Pricing", "sectionSubtitle": "Choose the plan that best fits your needs, all plans include core features", "popularLabel": "Most Popular", "freePlan": {"title": "Free", "price": "$0", "description": "Perfect for first-time users and creative exploration", "buttonText": "Get Started", "credits": "10 credits/day", "features": {"dailyCredits": "10 credits per day", "trialPeriod": "14-day trial of all models", "resultStorage": "Results saved for one month", "privateResults": "Cannot save as private", "commercialUse": "No commercial use", "advertisements": "Includes advertisements"}}, "basicPlan": {"title": "Basic", "price": "$9.9/month", "description": "Ideal for individual creators and small projects", "buttonText": "Choose Basic", "credits": "400 credits/month", "features": {"monthlyCredits": "400 credits per month", "noAds": "No advertisements", "longTermStorage": "Long-term result storage", "privateResults": "Private generation results", "commercialUse": "Commercial use", "unlimitedPrompts": "Unlimited smart prompts"}}, "proPlan": {"title": "Pro", "price": "$29.9/month", "description": "For professional creators and commercial use", "buttonText": "Choose Pro", "credits": "1300 credits/month", "features": {"monthlyCredits": "1300 credits per month", "unlimitedPrompts": "Unlimited smart prompts", "noAds": "No advertisements", "longTermStorage": "Long-term result storage", "privateResults": "Private generation results", "commercialUse": "Commercial use"}}}, "models": {"sectionTitle": "Powerful Flux Model Series", "sectionSubtitle": "From basic creation to professional image generation, the Flux model series meets various creative needs", "proLabel": "PRO", "ultraLabel": "ULTRA", "fluxSchnell": {"title": "Flux Schnell", "description": "Quickly generate basic images, suitable for proof of concept and creative exploration"}, "fluxDev": {"title": "Flux Dev", "description": "Developer-friendly model, supporting basic image generation and simple editing"}, "fluxPro": {"title": "Flux 1.1 Pro", "description": "Faster, better professional image generation, supporting high-quality creation"}, "fluxUltra": {"title": "Flux 1.1 Ultra", "description": "Ultra-high resolution and realism, suitable for professional creation and commercial use"}, "fluxFill": {"title": "Flux Fill", "description": "Powerful image editing and expansion features, supporting precise modifications and content filling"}, "fluxDepthCanny": {"title": "Flux Depth/Canny", "description": "Depth map extraction and edge training, supporting precise control of the image generation process"}}, "gallery": {"sectionTitle": "Editing Examples", "sectionSubtitle": "See real editing results", "viewMoreButton": "View More", "beforeAfter": {"before": "Before", "after": "After", "hoverToCompare": "Hover to compare"}}, "footer": {"product": "Product", "features": "Features", "pricing": "Pricing", "gallery": "Gallery", "resources": "Resources", "documentation": "Documentation", "tutorials": "Tutorials", "blog": "Blog", "company": "Company", "about": "About", "careers": "Careers", "contact": "Contact", "legal": "Legal", "terms": "Terms of Service", "privacy": "Privacy Policy", "cookies": "<PERSON><PERSON>", "copyright": "All rights reserved."}, "locale": {"switchLanguage": "Switch language", "languages": {"zh": "Chinese", "en": "English"}}, "labels": {"pro": "PRO"}, "generate": {"title": "Flux Image Generation", "textToImage": "Text to Image", "textToImageShort": "Text", "imageToImage": "Image Editing", "imageToImageShort": "Edit", "prompt": "Prompt", "promptDescription": "Describe the image you want to generate", "clickToChangeImage": "Click to change image", "dragAndDrop": "Drag and drop an image here or click to upload", "supportedFormats": "Supports JPG, PNG formats", "textToImagePlaceholder": "Enter a detailed image description, e.g.: A cute <PERSON><PERSON> Inu sitting on grass, sunny day, high-quality photography style...", "imageToImagePlaceholder": "Describe the modifications you want to make to the image, e.g.: Change the background to a beach scene, add sunset effects...", "smartOptimize": "Smart Optimize", "optimizing": "Optimizing...", "negativePrompt": "Negative Prompt", "negativePromptPlaceholder": "Enter elements you don't want in the image, e.g.: blur, low quality, distortion, unnatural...", "modelSelection": "Model Selection", "advancedSettings": "Advanced Settings", "hideAdvancedSettings": "Hide Advanced Settings", "generateImage": "Generate Image", "generating": "Generating...", "imageSize": "Image Size", "width": "<PERSON><PERSON><PERSON>", "height": "Height", "generationControl": "Generation Control", "generationCount": "Generation Count", "sheets": "images", "samplingSteps": "Sampling Steps", "steps": "steps", "guidanceScale": "Guidance Scale", "randomSeed": "Random Seed", "generationResults": "Generation Results", "generatingImages": "Generating your images, please wait...", "imagesWillAppearHere": "Your generated images will appear here", "generatedImageAlt": "Generated image {index}", "uploadedImageAlt": "Uploaded image", "selectModelPlaceholder": "Select model"}, "Generate": {"aiPowered": "AI-Powered Image Processing", "errors": {"uploadAndPrompt": "Please upload an image and enter a prompt", "processingFailed": "Failed to process image. Please try again.", "uploadFailed": "Failed to upload image. Please try again.", "uploadUrlFailed": "Failed to get upload URL. Please try again.", "invalidFileType": "Please upload a valid image file (JPG, JPEG, PNG)", "fileSizeLimit": "File size must be less than 10MB", "dropValidImages": "Please drop valid image files"}, "success": {"imageProcessed": "Image processed successfully!", "imageUploaded": "Image uploaded successfully!"}, "title": "AI Image Editor", "subtitle": "Upload images and edit them using text instructions", "processing": "Processing...", "generate": "Generate Edit", "features": {"title": "Editing Features", "description": "Select a feature to quickly edit your image", "enhance": {"name": "<PERSON><PERSON>ce", "template": "Enhance the image quality and make it look more professional"}, "retouch": {"name": "Retouch", "template": "Remove blemishes and imperfections from the portrait"}, "background": {"name": "Background", "template": "Change the background to "}, "style": {"name": "Style", "template": "Transform the image using style "}, "resize": {"name": "Resize", "template": "Resize the image while maintaining quality"}}, "prompt": {"label": "Editing Instructions", "placeholder": "Describe how you want to edit the image in detail...", "help": "Be specific about what changes you want to make", "suggestions": {"title": "Quick Suggestions", "hide": "Hide suggestions", "show": "Show suggestions"}}, "advanced": {"title": "Advanced Settings", "quality": "Output Quality", "preserve": "Preserve Original Aspects", "format": "Output Format", "enhance": "<PERSON><PERSON>ce <PERSON>"}, "image": {"title": "Image", "description": "Upload an image to edit", "upload": "Upload Your Image", "dropOrClick": "Drop your image here or click to browse", "maxSize": "Max 10MB", "formats": "Supports JPG, PNG, WEBP, GIF (max 10MB)", "selectButton": "Select Image", "original": "Original", "result": "Edit Result", "comparison": "Before & After", "single": "Single View"}, "instructions": {"label": "Editing Instructions", "processingText": "Processing your image..."}, "suggestions": {"title": "Quick Actions", "removeBackground": "Remove background", "enhanceQuality": "Enhance quality", "portraitRetouch": "Portrait retouch", "changeBackground": "Change background", "artisticStyle": "Artistic style", "vintageEffect": "Vintage effect"}, "actions": {"download": "Download", "clear": "Clear", "reset": "Reset", "generate": "Generate Edit"}, "status": {"uploading": "Uploading...", "processing": "Processing your image...", "complete": "Processing complete!", "error": "Something went wrong. Please try again."}, "batch": {"title": "Batch Processing", "description": "Process multiple images at once", "limit": "Up to 5 images"}}, "privacy": {"title": "Privacy Policy", "lastUpdated": "Last Updated", "dataCollection": {"title": "Information Collection", "intro": "We collect the following types of information to provide and improve our services:", "personal": {"title": "Personal Information", "email": "Email address (for account creation and communication)", "name": "Name or username", "profile": "Profile information (such as avatar)"}, "usage": {"title": "Usage Data", "images": "Uploaded images and editing results", "prompts": "Editing instructions and prompts", "activity": "Usage activity and preference settings"}}, "dataUsage": {"title": "Information Usage", "intro": "We use the collected information for the following purposes:", "service": "Provide image editing services and features", "improvement": "Improve and optimize our AI models and service quality", "support": "Provide customer support and technical assistance", "communication": "Send service-related notifications and updates", "legal": "Comply with legal requirements and protect our rights"}, "dataSharing": {"title": "Information Sharing", "intro": "We do not sell your personal information. We only share information in the following circumstances:", "consent": "With your explicit consent", "providers": "With trusted third-party service providers (such as cloud storage, payment processing)", "legal": "Legal requirements or protection of legitimate interests", "business": "Business transfer or merger situations"}, "dataSecurity": {"title": "Data Security", "intro": "We implement various security measures to protect your information:", "encryption": "Data transmission and storage encryption", "access": "Strict access control and permission management", "monitoring": "Regular security monitoring and vulnerability scanning", "backup": "Secure data backup and recovery mechanisms"}, "userRights": {"title": "User Rights", "intro": "You have the following rights regarding your personal information:", "access": "Access and view your personal data", "correction": "Correct inaccurate or incomplete information", "deletion": "Request deletion of your personal data", "portability": "Obtain copies of your data", "objection": "Object to certain data processing activities"}, "cookies": {"title": "Cookies and Tracking Technologies", "intro": "We use cookies and similar technologies to improve user experience:", "essential": {"title": "Essential Cookies", "description": "Cookies necessary for the website to function properly"}, "analytics": {"title": "Analytics Cookies", "description": "Help us understand website usage and improve services"}}, "thirdParty": {"title": "Third-Party Services", "intro": "We use the following third-party services, which have their own privacy policies:", "auth": "Google authentication services", "storage": "Cloud storage services (for image storage)", "analytics": "Website analytics services", "payment": "Payment processing services"}, "dataRetention": {"title": "Data Retention", "intro": "We retain your data according to the following policies:", "account": "Account data: During account existence", "images": "Image data: According to your subscription plan", "logs": "Log data: Maximum 12 months"}, "childrenPrivacy": {"title": "Children's Privacy", "content": "Our services are not directed to children under 13. We do not knowingly collect personal information from children under 13. If we discover we have collected such information, we will delete it immediately."}, "policyUpdates": {"title": "Policy Updates", "content": "We may update this privacy policy from time to time. Significant changes will be notified via email or website notice. Continued use of our services indicates acceptance of the updated policy."}, "contact": {"title": "Contact Us", "intro": "If you have any questions or concerns about this privacy policy, please contact us:", "email": {"label": "Email"}, "address": {"label": "Address", "value": "FluxPix AI Solutions, 123 Innovation Blvd, San Francisco, CA 94105"}}}, "terms": {"title": "Terms of Service", "lastUpdated": "Last Updated", "serviceAgreement": {"title": "Service Agreement", "intro": "Welcome to our AI image editing service. By using our service, you agree to the following terms:", "acceptance": "Use of the service constitutes acceptance of these terms", "changes": "We reserve the right to modify these terms at any time", "eligibility": "Users must be 18 years or older or have guardian consent"}, "serviceDescription": {"title": "Service Description", "intro": "We provide artificial intelligence-based image editing services, including but not limited to:", "editing": "Image editing and processing features", "ai": "AI-driven automated editing tools", "storage": "Cloud image storage and management", "support": "Customer support and technical assistance"}, "userResponsibilities": {"title": "User Responsibilities", "intro": "As a user, you are responsible for:", "lawful": "Using our service lawfully", "rights": "Ensuring you have legal rights to uploaded content", "content": "Not uploading illegal, harmful, or infringing content", "security": "Protecting your account security and login information"}, "prohibitedUse": {"title": "Prohibited Use", "intro": "The following behaviors are strictly prohibited:", "illegal": "Any illegal or inappropriate activities", "harmful": "Uploading harmful, defamatory, or offensive content", "infringing": "Infringing on others' intellectual property rights", "malicious": "Malicious attacks or service disruption", "spam": "Sending spam or commercial promotion"}, "intellectualProperty": {"title": "Intellectual Property", "intro": "Intellectual property protection is an important part of our service:", "ourRights": {"title": "Our Rights", "description": "We own all rights to the service, software, technology, and related intellectual property"}, "userContent": {"title": "User Content", "description": "You retain ownership of uploaded content but grant us necessary permissions for processing and storage"}}, "paidServices": {"title": "Paid Services", "intro": "For paid services, the following terms apply:", "billing": "Fees will be automatically charged according to the selected plan", "refunds": "Refund policy depends on specific circumstances", "cancellation": "You may cancel your subscription at any time", "changes": "We reserve the right to adjust prices but will provide advance notice"}, "serviceAvailability": {"title": "Service Availability", "content": "We strive to maintain continuous service availability but do not guarantee 100% uptime. We may temporarily interrupt service for maintenance, updates, or other reasons."}, "disclaimer": {"title": "Disclaimer", "content": "Our service is provided 'as is' without any express or implied warranties. We do not guarantee the accuracy, reliability, or suitability of the service."}, "limitationOfLiability": {"title": "Limitation of Liability", "content": "To the maximum extent permitted by law, we are not liable for any indirect, incidental, special, or consequential damages. Our total liability does not exceed the fees you have paid to us."}, "termination": {"title": "Service Termination", "intro": "Service may be terminated under the following circumstances:", "userTermination": "You may terminate service by deleting your account at any time", "ourTermination": "We may terminate your account for violating terms", "effect": "Upon termination, your data may be deleted"}, "governingLaw": {"title": "Governing Law", "content": "These terms are governed by the laws of the People's Republic of China. Any disputes will be resolved through friendly negotiation, and if negotiation fails, will be submitted to a court of competent jurisdiction."}, "contact": {"title": "Contact Us", "intro": "If you have any questions about the terms of service, please contact us:", "email": {"label": "Email"}, "address": {"label": "Address", "value": "FluxPix AI Solutions, 123 Innovation Blvd, San Francisco, CA 94105"}}}, "metadata": {"home": {"title": "Flux Pix - AI Image Editor Home", "description": "Flux Pix - Professional AI image editing platform, offering free smart watermark removal, background replacement, object removal and more. Upload images for instant AI editing, supports JPG, PNG formats, fast and accurate online image processing tool.", "keywords": ["AI image editor home", "free watermark removal", "online background replacement", "smart image processing", "one-click editing", "AI photo editor", "image editing platform", "online photo editing"]}, "generate": {"title": "Online Image Editor - Flux Pix", "description": "Flux Pix Online Image Editor - Use AI technology to quickly edit images, supporting smart watermark removal, background replacement, and object removal. Free upload, AI one-click processing, supports JPG, PNG formats, maximum 10MB. Start editing now!", "keywords": ["online image editing", "AI image processing", "image editing tool", "smart editing", "image generation", "free photo editing", "AI photo editing", "online photo editor", "image processor", "smart cutout"]}, "notFound": {"title": "Page Not Found - Flux Pix", "description": "Sorry, the page you are looking for does not exist. Return to Flux Pix homepage to continue using our AI image editing services.", "keywords": ["404", "page not found", "AI image editor", "Flux Pix"]}}, "common": {"home": "Home", "loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "confirm": "Confirm", "close": "Close", "save": "Save", "edit": "Edit", "delete": "Delete", "back": "Back", "next": "Next", "previous": "Previous", "submit": "Submit"}, "notFound": {"title": "Page Not Found", "description": "Sorry, the page you are looking for does not exist or has been moved.", "backToHome": "Back to Home", "startEditing": "Start Editing", "helpfulLinks": {"title": "Helpful Links", "aiImageEditing": "AI Image Editing", "smartWatermarkRemoval": "Smart Watermark Removal", "backgroundReplacement": "Background Replacement"}, "supportLinks": {"title": "Support", "privacyPolicy": "Privacy Policy", "termsOfService": "Terms of Service", "contactSupport": "Contact Support"}}, "profile": {"title": "Profile", "subtitle": "Manage your account information and settings", "loading": "Loading...", "welcome": "Welcome to Flux Pix", "anonymousUser": "Anonymous User", "email": "Email Address", "name": "Name", "joinedDate": "Joined Date", "notSet": "Not Set", "signOut": "Sign Out"}, "credits": {"title": "Credits Management", "currentCredits": "Current Credits", "totalCredits": "Total Credits", "usedCredits": "Used Credits", "availableCredits": "Available Credits", "dailyCredits": "Daily Credits", "lastRefresh": "Last Refresh", "userType": {"title": "User Type", "free": "Free User", "premium": "Premium User", "pro": "Pro User"}, "history": {"title": "Credits History", "type": {"daily_grant": "Daily Grant", "usage": "Usage", "refund": "Refund", "purchase": "Purchase", "bonus": "Bonus"}, "description": {"dailyRefresh": "Daily credits refresh", "aiImageEdit": "AI image editing", "aiImageEditFailed": "AI image editing failed, credits refunded", "newUserBonus": "New user daily credits"}}, "insufficient": {"title": "Insufficient Credits", "message": "This operation requires {required} credits, but you currently have only {available} credits.", "dailyRefresh": "Daily Free Credits", "timeUntilRefresh": "{hours} hours {minutes} minutes until next credit refresh", "dailyAmount": "Get 10 free credits daily", "tryLater": "Try Later", "learnMore": "Learn More"}, "display": {"perEdit": "1 credit per edit", "refreshedToday": "Refreshed today"}, "config": {"title": "Credit Configuration", "dailyCredits": "Daily Credits", "maxDailyCredits": "Max Daily Credits", "newUserBonus": "New User Bonus", "referralBonus": "Referral Bonus", "features": {"canSavePrivate": "Private Save", "canUseCommercially": "Commercial Use", "hasAds": "<PERSON><PERSON> Display", "maxStorageDays": "Storage Days", "prioritySupport": "Priority Support", "unlimited": "Unlimited", "enabled": "Enabled", "disabled": "Disabled"}}}, "generation": {"title": "AI Image Generation", "processing": "Processing your image...", "success": "Image processed successfully!", "failed": "Image processing failed", "errors": {"invalidPrompt": "Edit instruction cannot be empty", "invalidImageUrl": "Invalid image URL format", "invalidModel": "Invalid AI model", "aiProcessingFailed": "AI processing failed: No image generated", "uploadFailed": "Image upload failed", "insufficientCredits": "Insufficient credits for image editing", "processingError": "Image processing failed: {message}", "genericError": "Image processing failed, please try again later"}, "result": {"originalImage": "Original Image", "generatedImage": "Generated Result", "prompt": "Edit Instruction", "creditsUsed": "Credits Used", "processingTime": "Processing Time", "viewOriginal": "View Original"}, "status": {"queueUpdate": "Queue Update", "aiProcessing": "AI Processing", "uploadingToStorage": "Uploading to Storage", "completed": "Processing Completed"}}}